from server.core.db import db

class Achievement:
    """成就系统"""
    def __init__(self):
        self.table_name = "achievements"
    
    @staticmethod
    def create_table():
        """创建成就表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS achievements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                type INTEGER NOT NULL,  # 1=战斗, 2=探索, 3=收集, 4=社交
                condition_type TEXT NOT NULL,  # kill_count/item_collected/quest_completed等
                condition_value INTEGER NOT NULL,
                reward_exp INTEGER DEFAULT 0,
                reward_gold INTEGER DEFAULT 0,
                reward_item_id INTEGER,
                reward_item_quantity INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()

    @staticmethod
    def get_all():
        """获取所有成就"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM achievements')
        return cursor.fetchall()

    @staticmethod
    def get_by_id(achievement_id):
        """根据ID获取成就"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM achievements WHERE id = ?', (achievement_id,))
        return cursor.fetchone()

class AchievementProgress:
    """成就进度跟踪"""
    def __init__(self):
        self.table_name = "achievement_progress"
    
    @staticmethod
    def create_table():
        """创建成就进度表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS achievement_progress (
                character_id INTEGER NOT NULL,
                achievement_id INTEGER NOT NULL,
                progress INTEGER DEFAULT 0,
                unlocked BOOLEAN DEFAULT FALSE,
                unlocked_at TIMESTAMP,
                reward_claimed BOOLEAN DEFAULT FALSE,
                PRIMARY KEY (character_id, achievement_id),
                FOREIGN KEY (character_id) REFERENCES characters(id),
                FOREIGN KEY (achievement_id) REFERENCES achievements(id)
            )
        ''')
        conn.commit()

    @staticmethod
    def update_progress(character_id, achievement_id, progress):
        """更新成就进度"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT OR IGNORE INTO achievement_progress 
            (character_id, achievement_id, progress)
            VALUES (?, ?, ?)
        ''', (character_id, achievement_id, progress))
        
        cursor.execute('''
            UPDATE achievement_progress 
            SET progress = ?
            WHERE character_id = ? AND achievement_id = ?
        ''', (progress, character_id, achievement_id))
        conn.commit()

    @staticmethod
    def unlock_achievement(character_id, achievement_id):
        """解锁成就"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE achievement_progress 
            SET unlocked = TRUE, unlocked_at = CURRENT_TIMESTAMP
            WHERE character_id = ? AND achievement_id = ? AND progress >= (
                SELECT condition_value FROM achievements WHERE id = ?
            )
        ''', (character_id, achievement_id, achievement_id))
        conn.commit()

    @staticmethod
    def claim_reward(character_id, achievement_id):
        """领取成就奖励"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE achievement_progress 
            SET reward_claimed = TRUE
            WHERE character_id = ? AND achievement_id = ? AND unlocked = TRUE
        ''', (character_id, achievement_id))
        conn.commit()

    @staticmethod
    def get_character_achievements(character_id):
        """获取角色的所有成就状态"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT a.*, ap.progress, ap.unlocked, ap.unlocked_at, ap.reward_claimed
            FROM achievements a
            LEFT JOIN achievement_progress ap ON a.id = ap.achievement_id AND ap.character_id = ?
        ''', (character_id,))
        return cursor.fetchall()