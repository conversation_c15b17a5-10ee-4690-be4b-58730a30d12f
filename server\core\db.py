import sqlite3
from pathlib import Path
import logging
from typing import Optional, Tuple

class Database:
    """数据库连接管理类"""
    
    def __init__(self):
        self.conn: Optional[sqlite3.Connection] = None
        self.cursor: Optional[sqlite3.Cursor] = None
        self._db_path = Path(__file__).parent.parent.parent / "game.db"
        
    def get_db(self) -> Tuple[sqlite3.Connection, sqlite3.Cursor]:
        """获取数据库连接和游标"""
        if not self.conn:
            try:
                self.conn = sqlite3.connect(str(self._db_path), check_same_thread=False)
                self.conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
                self.cursor = self.conn.cursor()
                logging.info(f"数据库连接成功: {self._db_path}")
            except Exception as e:
                logging.error(f"数据库连接失败: {e}")
                raise
        return self.conn, self.cursor
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        self.conn = None
        self.cursor = None
        logging.info("数据库连接已关闭")
    
    def execute_query(self, query: str, params: tuple = None) -> list:
        """执行查询并返回结果"""
        conn, cursor = self.get_db()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
        except Exception as e:
            logging.error(f"查询执行失败: {query}, 错误: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        conn, cursor = self.get_db()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logging.error(f"更新执行失败: {query}, 错误: {e}")
            conn.rollback()
            raise
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        conn, cursor = self.get_db()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.lastrowid
        except Exception as e:
            logging.error(f"插入执行失败: {query}, 错误: {e}")
            conn.rollback()
            raise

# 全局数据库实例
db = Database()

class BaseModel:
    """数据模型基类"""
    
    def __init__(self):
        self.table_name = ""
    
    @classmethod
    def get_db_connection(cls):
        """获取数据库连接"""
        conn, _ = db.get_db()
        return conn
    
    @classmethod
    def execute_query(cls, query: str, params: tuple = None) -> list:
        """执行查询"""
        return db.execute_query(query, params)
    
    @classmethod
    def execute_update(cls, query: str, params: tuple = None) -> int:
        """执行更新"""
        return db.execute_update(query, params)
    
    @classmethod
    def execute_insert(cls, query: str, params: tuple = None) -> int:
        """执行插入"""
        return db.execute_insert(query, params)
