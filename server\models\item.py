from server.core.db import db
from enum import Enum

class ItemType(Enum):
    """物品类型枚举"""
    EQUIPMENT = 1  # 可装备物品
    CONSUMABLE = 2  # 消耗品
    MATERIAL = 3  # 材料
    QUEST = 4  # 任务物品

class EquipmentSlot(Enum):
    """装备槽位枚举"""
    HEAD = 1
    CHEST = 2
    HANDS = 3
    LEGS = 4
    FEET = 5
    WEAPON = 6
    ACCESSORY = 7

class Item:
    """物品基础模型"""
    def __init__(self):
        self.table_name = "items"
        self.id = None
        self.name = ""
        self.type = None
        self.description = ""
        self.value = 0  # 基础价值

    @staticmethod
    def create_table():
        """创建物品表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type INTEGER NOT NULL,
                description TEXT,
                value INTEGER DEFAULT 0
            )
        ''')
        conn.commit()

    @staticmethod
    def create(name, item_type, description="", value=0):
        """创建新物品"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO items 
            (name, type, description, value)
            VALUES (?, ?, ?, ?)
        ''', (name, item_type.value, description, value))
        conn.commit()
        return cursor.lastrowid

    @staticmethod
    def get_by_id(item_id):
        """根据ID获取物品"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM items WHERE id = ?', (item_id,))
        return cursor.fetchone()

    @staticmethod
    def get_all():
        """获取所有物品"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM items')
        return cursor.fetchall()

    @staticmethod
    def get_by_type(item_type):
        """根据类型获取物品"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM items WHERE type = ?', (item_type.value,))
        return cursor.fetchall()

class Equipment(Item):
    """装备模型"""
    def __init__(self):
        super().__init__()
        self.slot = None
        self.attack_bonus = 0
        self.defense_bonus = 0
        self.hp_bonus = 0
        self.durability = 100

    @staticmethod
    def create_table():
        """创建装备表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment (
                id INTEGER PRIMARY KEY,
                slot INTEGER NOT NULL,
                attack_bonus INTEGER DEFAULT 0,
                defense_bonus INTEGER DEFAULT 0,
                hp_bonus INTEGER DEFAULT 0,
                durability INTEGER DEFAULT 100,
                FOREIGN KEY (id) REFERENCES items(id)
            )
        ''')
        conn.commit()

    @staticmethod
    def create_equipment(item_id, slot, attack=0, defense=0, hp=0, durability=100):
        """创建新装备"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO equipment 
            (id, slot, attack_bonus, defense_bonus, hp_bonus, durability)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (item_id, slot.value, attack, defense, hp, durability))
        conn.commit()