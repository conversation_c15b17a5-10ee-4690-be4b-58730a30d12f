from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from datetime import datetime, timedelta
import jwt
from typing import Optional, Dict, Any
import logging
from server.config import JWT_CONFIG

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/login")

# JWT配置
SECRET_KEY = JWT_CONFIG.secret_key
ALGORITHM = JWT_CONFIG.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = JWT_CONFIG.access_token_expire_minutes

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        phone: str = payload.get("sub")
        if phone is None:
            return None
        return {"phone": phone, "exp": payload.get("exp")}
    except jwt.PyJWTError as e:
        logging.warning(f"JWT验证失败: {e}")
        return None

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """获取当前认证用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # 验证令牌
    token_data = verify_token(token)
    if token_data is None:
        raise credentials_exception
    
    # 获取用户信息
    from server.models.user import User
    user = User.get_by_phone(token_data["phone"])
    if user is None:
        raise credentials_exception
    
    return user

def hash_password(password: str) -> str:
    """哈希密码"""
    import hashlib
    import secrets
    
    # 生成盐值
    salt = secrets.token_hex(16)
    # 创建哈希
    pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    # 返回盐值和哈希值的组合
    return salt + pwd_hash.hex()

def verify_password(password: str, hashed_password: str) -> bool:
    """验证密码"""
    import hashlib
    
    try:
        # 提取盐值（前32个字符）
        salt = hashed_password[:32]
        # 提取哈希值
        stored_hash = hashed_password[32:]
        # 计算输入密码的哈希值
        pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
        # 比较哈希值
        return pwd_hash.hex() == stored_hash
    except Exception as e:
        logging.error(f"密码验证失败: {e}")
        return False

class AuthManager:
    """认证管理器"""
    
    @staticmethod
    def authenticate_user(phone: str, password: str) -> Optional[Dict[str, Any]]:
        """认证用户"""
        from server.models.user import User
        user = User.get_by_phone(phone)
        if not user:
            return None
        
        if not verify_password(password, user["password"]):
            return None
        
        return user
    
    @staticmethod
    def create_user_token(user: Dict[str, Any]) -> str:
        """为用户创建访问令牌"""
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["phone"]}, 
            expires_delta=access_token_expires
        )
        return access_token
