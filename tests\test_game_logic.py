"""
游戏逻辑测试
"""

import pytest
from server.core.game_logic import GameLogic
from server.models.user import User
from server.models.character import Character
from server.models.attributes import Attributes
from server.models.skill import Skill
from server.models.achievement import Achievement, AchievementProgress
from server.models.quest import Quest, QuestProgress

class TestGameLogic:
    """游戏逻辑测试"""
    
    def test_calculate_level_exp(self):
        """测试等级经验计算"""
        # 测试不同等级的经验需求
        assert GameLogic.calculate_level_exp(1) == 100
        assert GameLogic.calculate_level_exp(2) == 250  # 2*100 + 1*50
        assert GameLogic.calculate_level_exp(3) == 450  # 3*100 + 2*50
        assert GameLogic.calculate_level_exp(5) == 1000 # 5*100 + 4*50
    
    def test_check_level_up(self, test_db):
        """测试等级提升检查"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138010",
            password="test123",
            nickname="测试用户10"
        )
        character_id = Character.create(user["id"], "测试角色7")
        
        # 给角色足够的经验值升级
        from server.core.db import db
        conn, cursor = db.get_db()
        cursor.execute(
            'UPDATE character SET exp = ? WHERE id = ?',
            (250, character_id)  # 足够升到2级的经验
        )
        conn.commit()
        
        # 检查升级
        result = GameLogic.check_level_up(character_id)
        assert result["level_up"] == True
        assert result["new_level"] == 2
        
        # 验证角色等级已更新
        character = Character.get_by_id(character_id)
        assert character[3] == 2  # level字段
    
    def test_check_level_up_insufficient_exp(self, test_db):
        """测试经验不足时不升级"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138011",
            password="test123",
            nickname="测试用户11"
        )
        character_id = Character.create(user["id"], "测试角色8")
        
        # 给角色不足的经验值
        from server.core.db import db
        conn, cursor = db.get_db()
        cursor.execute(
            'UPDATE character SET exp = ? WHERE id = ?',
            (50, character_id)  # 不足升级的经验
        )
        conn.commit()
        
        # 检查升级
        result = GameLogic.check_level_up(character_id)
        assert result["level_up"] == False
    
    def test_apply_skill_effect_heal(self, test_db):
        """测试治疗技能效果"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138012",
            password="test123",
            nickname="测试用户12"
        )
        character_id = Character.create(user["id"], "测试角色9")
        
        # 创建治疗技能
        skill_id = Skill.create(
            name="治疗术",
            description="恢复生命值",
            effect="heal 50hp",
            mana_cost=10,
            cooldown=0,
            required_level=1
        )
        
        # 设置角色HP和MP
        from server.core.db import db
        conn, cursor = db.get_db()
        cursor.execute(
            'UPDATE character SET hp = ?, mp = ? WHERE id = ?',
            (50, 50, character_id)
        )
        conn.commit()
        
        # 应用技能效果
        result = GameLogic.apply_skill_effect(character_id, skill_id)
        assert result["success"] == True
        assert "heal_amount" in result
        assert result["mp_cost"] == 10
    
    def test_apply_skill_effect_insufficient_mp(self, test_db):
        """测试MP不足时技能失败"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138013",
            password="test123",
            nickname="测试用户13"
        )
        character_id = Character.create(user["id"], "测试角色10")
        
        # 创建高消耗技能
        skill_id = Skill.create(
            name="大火球术",
            description="强力火球",
            effect="attack fire damage",
            mana_cost=100,  # 高MP消耗
            cooldown=0,
            required_level=1
        )
        
        # 设置角色低MP
        from server.core.db import db
        conn, cursor = db.get_db()
        cursor.execute(
            'UPDATE character SET mp = ? WHERE id = ?',
            (10, character_id)  # 不足的MP
        )
        conn.commit()
        
        # 应用技能效果
        result = GameLogic.apply_skill_effect(character_id, skill_id)
        assert result["success"] == False
        assert "MP不足" in result["message"]
    
    def test_complete_quest_objective(self, test_db):
        """测试完成任务目标"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138014",
            password="test123",
            nickname="测试用户14"
        )
        character_id = Character.create(user["id"], "测试角色11")
        
        # 创建任务
        quest_id = Quest.create(
            name="击败怪物",
            description="击败10只怪物",
            quest_type=1,
            min_level=1,
            repeatable=False,
            reward_exp=100,
            reward_gold=50
        )
        
        # 开始任务
        QuestProgress.start_quest(character_id, quest_id)
        
        # 获取角色初始经验和金币
        character_before = Character.get_by_id(character_id)
        exp_before = character_before[4]  # exp字段
        gold_before = character_before[9]  # gold字段
        
        # 完成任务目标
        completed_quests = GameLogic.complete_quest_objective(
            character_id, "kill_monster", 10
        )
        
        assert len(completed_quests) > 0
        assert completed_quests[0]["quest_id"] == quest_id
        assert completed_quests[0]["reward_exp"] == 100
        assert completed_quests[0]["reward_gold"] == 50
        
        # 验证奖励已发放
        character_after = Character.get_by_id(character_id)
        assert character_after[4] == exp_before + 100  # 经验增加
        assert character_after[9] == gold_before + 50   # 金币增加
    
    def test_calculate_battle_damage(self):
        """测试战斗伤害计算"""
        attacker_stats = {
            "attack": 20,
            "critical_rate": 10
        }
        
        defender_stats = {
            "defense": 5
        }
        
        # 计算伤害
        damage = GameLogic.calculate_battle_damage(attacker_stats, defender_stats)
        
        # 伤害应该大于0且考虑了防御
        assert damage > 0
        assert damage >= 1  # 最小伤害为1
    
    def test_calculate_battle_damage_with_skill(self):
        """测试带技能加成的战斗伤害"""
        attacker_stats = {
            "attack": 15,
            "critical_rate": 5
        }
        
        defender_stats = {
            "defense": 3
        }
        
        skill_bonus = 10
        
        # 计算带技能加成的伤害
        damage = GameLogic.calculate_battle_damage(
            attacker_stats, defender_stats, skill_bonus
        )
        
        # 伤害应该比基础伤害更高
        base_damage = GameLogic.calculate_battle_damage(attacker_stats, defender_stats)
        # 由于有随机因素，我们只检查伤害大于0
        assert damage > 0
    
    def test_get_character_combat_stats(self, test_db):
        """测试获取角色战斗属性"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138015",
            password="test123",
            nickname="测试用户15"
        )
        character_id = Character.create(user["id"], "测试角色12")
        
        # 获取战斗属性
        combat_stats = GameLogic.get_character_combat_stats(character_id)
        
        # 验证返回的属性
        assert "hp" in combat_stats
        assert "max_hp" in combat_stats
        assert "mp" in combat_stats
        assert "max_mp" in combat_stats
        assert "attack" in combat_stats
        assert "defense" in combat_stats
        assert "critical_rate" in combat_stats
        assert "dodge_rate" in combat_stats
        
        # 验证属性值合理
        assert combat_stats["hp"] > 0
        assert combat_stats["attack"] > 0
        assert combat_stats["defense"] >= 0
    
    def test_check_achievements(self, test_db):
        """测试成就检查"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138016",
            password="test123",
            nickname="测试用户16"
        )
        character_id = Character.create(user["id"], "测试角色13")
        
        # 创建等级成就
        achievement_id = Achievement.create(
            name="新手",
            description="达到5级",
            achievement_type=1,
            condition_type="level_up",
            condition_value=5,
            reward_exp=50,
            reward_gold=25
        )
        
        # 检查成就（角色达到5级）
        GameLogic.check_achievements(character_id, "level_up", 5)
        
        # 验证成就进度已更新
        achievements = AchievementProgress.get_character_achievements(character_id)
        
        # 找到我们创建的成就
        target_achievement = None
        for ach in achievements:
            if ach[0] == achievement_id:  # achievement id
                target_achievement = ach
                break
        
        assert target_achievement is not None
        # 检查进度是否已更新（具体字段位置可能需要调整）
        # assert target_achievement[7] == 5  # progress字段
