from fastapi import APIRouter, Depends, HTTPException
from server.models.character import Character
from server.core.auth import get_current_user
from typing import Dict, Any

router = APIRouter()

@router.post("/{character_id}/increase-attribute")
async def increase_attribute(
    character_id: int,
    attribute_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """提升角色属性"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    try:
        attribute = attribute_data.get("attribute")
        amount = attribute_data.get("amount", 1)
        updated_character = Character.increase_attribute(character_id, attribute, amount)
        return {
            "success": True,
            "character": updated_character
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))