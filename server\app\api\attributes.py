from fastapi import APIRouter, Depends, HTTPException
from server.models.character import Character
from server.models.attributes import Attributes
from server.core.auth import get_current_user
from typing import Dict, Any
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1/attributes", tags=["attributes"])

class AttributeUpdate(BaseModel):
    attribute: str
    amount: int = 1

@router.get("/character/{character_id}")
async def get_character_attributes(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色属性"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权访问该角色")

    attributes = Attributes.get_by_character_id(character_id)
    if not attributes:
        # 如果没有属性记录，创建默认属性
        Attributes.create_default_attributes(character_id)
        attributes = Attributes.get_by_character_id(character_id)

    # 计算衍生属性
    derived_stats = Attributes.calculate_derived_stats(attributes)

    return {
        "code": 200,
        "data": {
            "attributes": attributes,
            "derived_stats": derived_stats
        }
    }

@router.post("/character/{character_id}/increase")
async def increase_attribute(
    character_id: int,
    attribute_data: AttributeUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """提升角色属性"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")

    try:
        success = Attributes.increase_attribute(
            character_id,
            attribute_data.attribute,
            attribute_data.amount
        )
        if not success:
            raise HTTPException(status_code=400, detail="属性更新失败")

        # 获取更新后的属性
        updated_attributes = Attributes.get_by_character_id(character_id)
        derived_stats = Attributes.calculate_derived_stats(updated_attributes)

        return {
            "code": 200,
            "message": "属性提升成功",
            "data": {
                "attributes": updated_attributes,
                "derived_stats": derived_stats
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))