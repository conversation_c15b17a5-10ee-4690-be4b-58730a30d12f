from server.models.monster import Monster

def init_monster_data():
    """初始化怪物数据"""
    monsters = [
        {
            "name": "小史莱姆",
            "level": 1,
            "hp": 50,
            "attack": 5,
            "defense": 2,
            "exp_reward": 10,
            "item_drop_rate": 0.2,
            "drop_items": [1, 2]  # 小血瓶和小蓝瓶
        },
        {
            "name": "哥布林",
            "level": 3,
            "hp": 80,
            "attack": 12,
            "defense": 5,
            "exp_reward": 25,
            "item_drop_rate": 0.3
        },
        {
            "name": "骷髅兵",
            "level": 5,
            "hp": 120,
            "attack": 18,
            "defense": 8,
            "exp_reward": 40,
            "item_drop_rate": 0.4
        },
        {
            "name": "巨魔",
            "level": 8,
            "hp": 200,
            "attack": 30,
            "defense": 15,
            "exp_reward": 70,
            "item_drop_rate": 0.5
        }
    ]

    conn = Monster.get_db_connection()
    cursor = conn.cursor()
    
    # 清空现有怪物数据
    cursor.execute("DELETE FROM monsters")
    
    # 插入新怪物数据
    for monster in monsters:
        cursor.execute(
            """
            INSERT INTO monsters (
                name, level, hp, attack, defense, exp_reward, item_drop_rate
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            (
                monster["name"],
                monster["level"],
                monster["hp"],
                monster["attack"],
                monster["defense"],
                monster["exp_reward"],
                monster["item_drop_rate"]
            )
        )
    
    conn.commit()
    print(f"成功初始化 {len(monsters)} 种怪物数据")

if __name__ == "__main__":
    init_monster_data()