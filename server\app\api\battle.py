from fastapi import APIRouter, Depends, HTTPException
from server.models.battle import Battle
from server.models.character import Character
from server.core.auth import get_current_user
from typing import Dict, Any

router = APIRouter()

@router.post("/{character_id}/fight/{enemy_id}")
async def start_battle(
    character_id: int,
    enemy_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """发起战斗"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    # 获取敌人数据
    from server.models.enemy import Enemy
    enemy = Enemy.get_by_id(enemy_id)
    if not enemy:
        raise HTTPException(status_code=404, detail="敌人不存在")
    
    success, battle_log = Battle.fight(character, enemy)
    return {
        "victory": success,
        "log": battle_log,
        "character_hp": character["hp"]
    }

@router.get("/{character_id}/history")
async def get_battle_history(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取战斗历史"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    return Battle.get_character_battles(character_id)