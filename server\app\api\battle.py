from fastapi import APIRouter, Depends, HTTPException
from server.models.battle import Battle
from server.models.character import Character
from server.models.user import User
from server.core.auth import get_current_user
from server.core.db import db
from typing import Dict, Any

router = APIRouter(prefix="/api/v1/battle", tags=["battle"])

@router.post("/start")
async def start_battle(
    monster_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """开始战斗"""
    # 获取用户当前角色
    user_model = User()
    active_character = user_model.get_active_character(current_user["id"])
    if not active_character:
        raise HTTPException(status_code=400, detail="请先选择角色")

    # 获取怪物数据
    from server.models.monster import Monster
    monster = Monster.get_by_id(monster_id)
    if not monster:
        raise HTTPException(status_code=404, detail="怪物不存在")

    # 创建战斗ID
    battle_id = f"{active_character['id']}_{monster_id}"

    return {
        "battle_id": battle_id,
        "character": {
            "id": active_character["id"],
            "current_hp": active_character["hp"],
            "max_hp": active_character["max_hp"]
        },
        "monster": {
            "id": monster[0],
            "name": monster[1],
            "hp": monster[3],
            "attack": monster[4],
            "defense": monster[5]
        },
        "message": "战斗开始"
    }

@router.post("/round")
async def battle_round(
    battle_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """执行战斗回合"""
    # 解析battle_id
    try:
        character_id, monster_id = battle_id.split("_")
        character_id = int(character_id)
        monster_id = int(monster_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的战斗ID")

    # 获取角色和怪物数据
    character = Character.get_by_id(character_id)
    if not character or character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")

    from server.models.monster import Monster
    monster = Monster.get_by_id(monster_id)
    if not monster:
        raise HTTPException(status_code=404, detail="怪物不存在")

    # 简单的战斗逻辑
    import random

    # 角色攻击怪物
    character_attack = character[7] + random.randint(1, 10)  # strength + random
    damage_to_monster = max(1, character_attack - monster[5])  # attack - defense

    # 怪物攻击角色
    monster_attack = monster[4] + random.randint(1, 5)
    damage_to_character = max(1, monster_attack - character[8])  # attack - agility as defense

    # 更新HP
    new_monster_hp = max(0, monster[3] - damage_to_monster)
    new_character_hp = max(0, character[5] - damage_to_character)

    # 检查战斗是否结束
    battle_over = new_monster_hp <= 0 or new_character_hp <= 0
    winner = None
    exp_gained = 0

    if battle_over:
        if new_monster_hp <= 0:
            winner = "character"
            exp_gained = monster[6]  # exp_reward
            # 更新角色经验
            conn, cursor = db.get_db()
            cursor.execute(
                'UPDATE character SET exp = exp + ? WHERE id = ?',
                (exp_gained, character_id)
            )
            conn.commit()
        else:
            winner = "monster"

    # 更新角色HP
    conn, cursor = db.get_db()
    cursor.execute(
        'UPDATE character SET hp = ? WHERE id = ?',
        (new_character_hp, character_id)
    )
    conn.commit()

    result = {
        "battle_over": battle_over,
        "winner": winner,
        "round_result": {
            "damage_to_monster": damage_to_monster,
            "damage_to_character": damage_to_character,
            "character_hp_left": new_character_hp,
            "monster_hp_left": new_monster_hp
        }
    }

    if battle_over and winner == "character":
        result["exp_gained"] = exp_gained

    return result

@router.get("/{character_id}/history")
async def get_battle_history(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取战斗历史"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    return Battle.get_character_battles(character_id)