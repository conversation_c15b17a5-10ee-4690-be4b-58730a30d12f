from server.core.db import BaseModel, db
import json

class Monster(BaseModel):
    """
    怪物模型类
    """
    
    def __init__(self):
        super().__init__()
        self.table_name = "monsters"
        self.drop_items = []  # 存储可能的掉落物品ID列表
        
    @classmethod
    def create_table(cls):
        """
        创建怪物表
        """
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monsters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                level INTEGER DEFAULT 1,
                hp INTEGER NOT NULL,
                attack INTEGER NOT NULL,
                defense INTEGER NOT NULL,
                exp_reward INTEGER NOT NULL,
                item_drop_rate REAL DEFAULT 0.1,
                drop_items TEXT DEFAULT '[]'  -- 存储JSON格式的物品ID列表
            )
        ''')
        
        conn.commit()

    @classmethod
    def create(cls, name, level, hp, attack, defense, exp_reward, item_drop_rate=0.1, drop_items=None):
        """创建新怪物"""
        if drop_items is None:
            drop_items = []

        conn = cls.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO monsters
            (name, level, hp, attack, defense, exp_reward, item_drop_rate, drop_items)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, level, hp, attack, defense, exp_reward, item_drop_rate, json.dumps(drop_items)))
        conn.commit()
        return cursor.lastrowid

    @classmethod
    def get_by_id(cls, monster_id):
        """根据ID获取怪物"""
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM monsters WHERE id = ?', (monster_id,))
        result = cursor.fetchone()
        if result:
            # 解析drop_items JSON
            result = list(result)
            result[7] = json.loads(result[7]) if result[7] else []
        return result

    @classmethod
    def get_all(cls):
        """获取所有怪物"""
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM monsters')
        results = cursor.fetchall()
        for i, result in enumerate(results):
            result = list(result)
            result[7] = json.loads(result[7]) if result[7] else []
            results[i] = result
        return results
        
    @classmethod
    def get_by_id(cls, monster_id: int):
        """
        根据ID获取怪物数据
        :param monster_id: 怪物ID
        :return: 怪物数据字典或None
        """
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM monsters WHERE id = ?
        ''', (monster_id,))
        
        result = cursor.fetchone()
        if result:
            return {
                "id": result[0],
                "name": result[1],
                "level": result[2],
                "hp": result[3],
                "attack": result[4],
                "defense": result[5],
                "exp_reward": result[6],
                "item_drop_rate": result[7]
            }
        return None