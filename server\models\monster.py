from server.models import BaseModel

class Monster(BaseModel):
    """
    怪物模型类
    """
    
    def __init__(self):
        super().__init__()
        self.table_name = "monsters"
        self.drop_items = []  # 存储可能的掉落物品ID列表
        
    @classmethod
    def create_table(cls):
        """
        创建怪物表
        """
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monsters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                level INTEGER DEFAULT 1,
                hp INTEGER NOT NULL,
                attack INTEGER NOT NULL,
                defense INTEGER NOT NULL,
                exp_reward INTEGER NOT NULL,
                item_drop_rate REAL DEFAULT 0.1,
                drop_items TEXT DEFAULT '[]'  -- 存储JSON格式的物品ID列表
            )
        ''')
        
        conn.commit()
        
    @classmethod
    def get_by_id(cls, monster_id: int):
        """
        根据ID获取怪物数据
        :param monster_id: 怪物ID
        :return: 怪物数据字典或None
        """
        conn = cls.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM monsters WHERE id = ?
        ''', (monster_id,))
        
        result = cursor.fetchone()
        if result:
            return {
                "id": result[0],
                "name": result[1],
                "level": result[2],
                "hp": result[3],
                "attack": result[4],
                "defense": result[5],
                "exp_reward": result[6],
                "item_drop_rate": result[7]
            }
        return None