version: '3.8'

services:
  # MUD游戏API服务
  mud-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG_MODE=false
      - ENVIRONMENT=production
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - SECRET_KEY=production-secret-key-change-this-in-production
    volumes:
      - ./game.db:/app/game.db
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - mud-api
    restart: unless-stopped

  # Redis缓存 (可选，用于会话管理)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL数据库 (可选，替代SQLite)
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=mud_game
      - POSTGRES_USER=mud_user
      - POSTGRES_PASSWORD=mud_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
