from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import J<PERSON><PERSON><PERSON>r, jwt
from server.models import User, JWT_CONFIG

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/login")

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, JWT_CONFIG['SECRET_KEY'], algorithms=[JWT_CONFIG['ALGORITHM']])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = User()
    user_data = user.get_user_by_id(int(user_id))
    if user_data is None:
        raise credentials_exception
        
    return user_data