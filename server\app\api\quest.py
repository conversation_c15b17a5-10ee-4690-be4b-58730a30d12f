from fastapi import APIRouter, Depends, HTTPException
from server.models.quest import Quest, QuestProgress
from server.models.character import Character
from server.core.auth import get_current_user

router = APIRouter(prefix="/api/v1/quests", tags=["quests"])

@router.get("/")
async def get_all_quests():
    """获取所有任务"""
    return {"code": 200, "data": Quest.get_all()}

@router.get("/{quest_id}")
async def get_quest(quest_id: int):
    """获取特定任务详情"""
    quest = Quest.get_by_id(quest_id)
    if not quest:
        raise HTTPException(status_code=404, detail="任务不存在")
    return {"code": 200, "data": quest}

@router.get("/character/{character_id}")
async def get_character_quests(
    character_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取角色的任务列表"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权访问该角色")

    quests = QuestProgress.get_character_quests(character_id)
    return {"code": 200, "data": quests}

@router.get("/character/{character_id}")
async def get_character_quests(character_id: int, user: dict = Depends(get_current_user)):
    """获取角色的所有任务状态"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    return QuestProgress.get_character_quests(character_id)

@router.post("/character/{character_id}/start/{quest_id}")
async def start_quest(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """开始任务"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="无权操作该角色")

    quest = Quest.get_by_id(quest_id)
    if not quest:
        raise HTTPException(status_code=404, detail="任务不存在")

    success = QuestProgress.start_quest(character_id, quest_id)
    if not success:
        raise HTTPException(status_code=400, detail="任务已经在进行中")

    return {"code": 200, "message": "任务开始"}

@router.post("/character/{character_id}/complete/{quest_id}")
async def complete_quest(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """完成任务"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="无权操作该角色")

    success = QuestProgress.complete_quest(character_id, quest_id)
    if not success:
        raise HTTPException(status_code=400, detail="无法完成该任务")

    return {"code": 200, "message": "任务完成"}

@router.post("/character/{character_id}/complete/{quest_id}")
async def complete_quest(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """完成任务"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    QuestProgress.complete_quest(character_id, quest_id)
    return {"message": "Quest completed"}

@router.post("/character/{character_id}/claim/{quest_id}")
async def claim_quest_reward(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """领取任务奖励"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    QuestProgress.claim_reward(character_id, quest_id)
    return {"message": "Reward claimed"}