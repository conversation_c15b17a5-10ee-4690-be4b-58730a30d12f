from fastapi import APIRouter, Depends, HTTPException
from server.models.quest import Quest, QuestProgress
from server.models.character import Character
from server.core.auth import get_current_user

router = APIRouter(prefix="/api/v1/quests", tags=["quests"])

@router.get("/")
async def get_all_quests():
    """获取所有任务"""
    return Quest.get_all()

@router.get("/{quest_id}")
async def get_quest(quest_id: int):
    """获取特定任务详情"""
    quest = Quest.get_by_id(quest_id)
    if not quest:
        raise HTTPException(status_code=404, detail="Quest not found")
    return quest

@router.get("/character/{character_id}")
async def get_character_quests(character_id: int, user: dict = Depends(get_current_user)):
    """获取角色的所有任务状态"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    return QuestProgress.get_character_quests(character_id)

@router.post("/character/{character_id}/start/{quest_id}")
async def start_quest(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """开始任务"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    quest = Quest.get_by_id(quest_id)
    if not quest:
        raise HTTPException(status_code=404, detail="Quest not found")
    
    QuestProgress.start_quest(character_id, quest_id)
    return {"message": "Quest started"}

@router.post("/character/{character_id}/complete/{quest_id}")
async def complete_quest(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """完成任务"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    QuestProgress.complete_quest(character_id, quest_id)
    return {"message": "Quest completed"}

@router.post("/character/{character_id}/claim/{quest_id}")
async def claim_quest_reward(character_id: int, quest_id: int, user: dict = Depends(get_current_user)):
    """领取任务奖励"""
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    QuestProgress.claim_reward(character_id, quest_id)
    return {"message": "Reward claimed"}