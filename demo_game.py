#!/usr/bin/env python3
"""
末日觉醒 MUD 游戏演示脚本
展示游戏的主要功能
"""

import requests
import json
import time

# 服务器配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class GameDemo:
    def __init__(self):
        self.token = None
        self.character_id = None
        
    def print_separator(self, title):
        print("\n" + "="*50)
        print(f" {title}")
        print("="*50)
    
    def register_user(self):
        """注册用户"""
        self.print_separator("用户注册")
        
        user_data = {
            "phone": "13800138000",
            "password": "demo123456",
            "nickname": "演示玩家"
        }
        
        try:
            response = requests.post(f"{API_BASE}/register", json=user_data)
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                print("✅ 用户注册成功")
                print(f"获得访问令牌: {self.token[:20]}...")
                return True
            else:
                print(f"❌ 注册失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 注册出错: {e}")
            return False
    
    def create_character(self):
        """创建角色"""
        self.print_separator("创建角色")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            response = requests.post(
                f"{API_BASE}/characters/create",
                params={"name": "末日战士"},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                self.character_id = data["data"]["character_id"]
                print("✅ 角色创建成功")
                print(f"角色ID: {self.character_id}")
                return True
            else:
                print(f"❌ 创建角色失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 创建角色出错: {e}")
            return False
    
    def show_character_info(self):
        """显示角色信息"""
        self.print_separator("角色信息")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # 获取角色基本信息
            response = requests.get(
                f"{API_BASE}/characters/{self.character_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                character = response.json()["data"]
                print(f"角色名称: {character[2]}")  # name
                print(f"等级: {character[3]}")      # level
                print(f"经验: {character[4]}")      # exp
                print(f"生命值: {character[5]}")    # hp
                print(f"魔法值: {character[6]}")    # mp
                print(f"金币: {character[9]}")      # gold
            
            # 获取角色属性
            response = requests.get(
                f"{API_BASE}/attributes/character/{self.character_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()["data"]
                attributes = data["attributes"]
                derived_stats = data["derived_stats"]
                
                print("\n基础属性:")
                print(f"  力量: {attributes['strength']}")
                print(f"  敏捷: {attributes['agility']}")
                print(f"  智力: {attributes['intelligence']}")
                print(f"  体质: {attributes['vitality']}")
                print(f"  幸运: {attributes['luck']}")
                
                print("\n衍生属性:")
                print(f"  最大生命值: {derived_stats['max_hp']}")
                print(f"  最大魔法值: {derived_stats['max_mp']}")
                print(f"  攻击力: {derived_stats['attack_power']}")
                print(f"  防御力: {derived_stats['defense']}")
                print(f"  暴击率: {derived_stats['critical_rate']}%")
                print(f"  闪避率: {derived_stats['dodge_rate']}%")
                
        except Exception as e:
            print(f"❌ 获取角色信息出错: {e}")
    
    def show_game_data(self):
        """显示游戏数据"""
        self.print_separator("游戏世界数据")
        
        try:
            # 显示物品
            response = requests.get(f"{API_BASE}/items/")
            if response.status_code == 200:
                items = response.json()["data"]
                print(f"📦 游戏中共有 {len(items)} 种物品")
                print("热门物品:")
                for item in items[:5]:
                    print(f"  - {item[1]} (价值: {item[4]} 金币)")
            
            # 显示技能
            response = requests.get(f"{API_BASE}/skills/")
            if response.status_code == 200:
                skills = response.json()["data"]
                print(f"\n⚔️ 游戏中共有 {len(skills)} 种技能")
                print("可学技能:")
                for skill in skills[:5]:
                    print(f"  - {skill[1]} (消耗: {skill[4]} MP, 需要等级: {skill[6]})")
            
            # 显示任务
            response = requests.get(f"{API_BASE}/quests/")
            if response.status_code == 200:
                quests = response.json()["data"]
                print(f"\n📋 游戏中共有 {len(quests)} 个任务")
                print("可接任务:")
                for quest in quests[:3]:
                    print(f"  - {quest[1]} (奖励: {quest[6]} 经验, {quest[7]} 金币)")
            
            # 显示成就
            response = requests.get(f"{API_BASE}/achievements/")
            if response.status_code == 200:
                achievements = response.json()["data"]
                print(f"\n🏆 游戏中共有 {len(achievements)} 个成就")
                print("成就列表:")
                for achievement in achievements[:3]:
                    print(f"  - {achievement[1]} (奖励: {achievement[5]} 经验)")
                    
        except Exception as e:
            print(f"❌ 获取游戏数据出错: {e}")
    
    def simulate_battle(self):
        """模拟战斗"""
        self.print_separator("战斗演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # 开始战斗
            print("🎯 挑战小史莱姆...")
            response = requests.post(
                f"{API_BASE}/battle/start",
                json={"monster_id": 1},
                headers=headers
            )
            
            if response.status_code == 200:
                battle_data = response.json()
                battle_id = battle_data["battle_id"]
                monster = battle_data["monster"]
                
                print(f"⚔️ 战斗开始！")
                print(f"对手: {monster['name']} (HP: {monster['hp']}, 攻击: {monster['attack']})")
                
                # 执行几个战斗回合
                round_count = 0
                while round_count < 5:  # 最多5回合演示
                    round_count += 1
                    print(f"\n--- 第 {round_count} 回合 ---")
                    
                    response = requests.post(
                        f"{API_BASE}/battle/round",
                        json={"battle_id": battle_id},
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        round_result = result["round_result"]
                        
                        print(f"你对{monster['name']}造成 {round_result['damage_to_monster']} 点伤害")
                        print(f"{monster['name']}对你造成 {round_result['damage_to_character']} 点伤害")
                        print(f"你的HP: {round_result['character_hp_left']}")
                        print(f"{monster['name']}的HP: {round_result['monster_hp_left']}")
                        
                        if result["battle_over"]:
                            if result["winner"] == "character":
                                print(f"\n🎉 胜利！获得 {result.get('exp_gained', 0)} 经验值")
                            else:
                                print(f"\n💀 战败...")
                            break
                    else:
                        print("战斗出错")
                        break
                        
                    time.sleep(1)  # 暂停1秒增加戏剧效果
                    
        except Exception as e:
            print(f"❌ 战斗出错: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎮 末日觉醒 MUD 游戏演示")
        print("欢迎来到末日后的世界...")
        
        # 检查服务器是否运行
        try:
            response = requests.get(BASE_URL)
            if response.status_code != 200:
                print("❌ 服务器未运行，请先启动服务器:")
                print("   python start_server.py")
                return
        except:
            print("❌ 无法连接到服务器，请确保服务器正在运行:")
            print("   python start_server.py")
            return
        
        # 执行演示步骤
        if not self.register_user():
            return
            
        if not self.create_character():
            return
            
        self.show_character_info()
        self.show_game_data()
        self.simulate_battle()
        
        self.print_separator("演示完成")
        print("🎉 演示完成！")
        print("你可以:")
        print("1. 访问 http://localhost:8000/docs 查看完整API文档")
        print("2. 使用API开发自己的游戏客户端")
        print("3. 扩展游戏功能和内容")
        print("\n感谢体验末日觉醒 MUD 游戏！")

if __name__ == "__main__":
    demo = GameDemo()
    demo.run_demo()
