from pydantic_settings import BaseSettings
from pydantic import Field, validator
from dotenv import load_dotenv
import os
from typing import Optional
import logging

# 加载.env文件
load_dotenv()

class DatabaseConfig(BaseSettings):
    host: str = Field(..., env="DB_HOST")
    port: int = Field(5432, env="DB_PORT")
    name: str = Field(..., env="DB_NAME")
    user: str = Field(..., env="DB_USER")
    password: str = Field(..., env="DB_PASSWORD")

    class Config:
        env_prefix = "DB_"
        secrets_dir = "/run/secrets"

class JWTConfig(BaseSettings):
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field("HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")

    @validator('secret_key')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError("密钥长度至少32字符")
        return v

class GameConfig(BaseSettings):
    max_characters_per_user: int = Field(3, env="MAX_CHARACTERS_PER_USER")
    starting_level: int = Field(1, env="STARTING_LEVEL")
    starting_gold: int = Field(100, env="STARTING_GOLD")

class ApiConfig(BaseSettings):
    host: str = Field("0.0.0.0", env="API_HOST")
    port: int = Field(8000, env="API_PORT")

class Settings(BaseSettings):
    debug: bool = Field(False, env="DEBUG_MODE")
    database: DatabaseConfig = DatabaseConfig()
    jwt: JWTConfig = JWTConfig()
    game: GameConfig = GameConfig()
    api: ApiConfig = ApiConfig()

    class Config:
        case_sensitive = False

# 初始化配置
try:
    settings = Settings()
    logging.info("配置加载成功")
except Exception as e:
    logging.error(f"配置加载失败: {str(e)}")
    raise

# 生产环境检查
if not settings.debug and os.getenv("ENVIRONMENT") != "production":
    logging.warning("当前运行在非生产环境但DEBUG模式已关闭!")

# 导出配置
DATABASE_CONFIG = settings.database
API_CONFIG = {
    "jwt": settings.jwt,
    "game": settings.game,
    "api": settings.api
}
JWT_CONFIG = API_CONFIG["jwt"]