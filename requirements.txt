# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 认证和安全
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# 配置管理
python-dotenv>=1.0.0
pydantic-settings>=2.0.0

# 数据库
# SQLite是Python内置的，不需要额外安装

# 开发和测试依赖
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.24.0
pytest-cov>=4.0.0

# 可选：生产环境依赖
# gunicorn>=21.0.0  # WSGI服务器
# psycopg2-binary>=2.9.0  # PostgreSQL驱动（如果使用PostgreSQL）
# redis>=4.0.0  # Redis客户端（如果使用Redis缓存）
