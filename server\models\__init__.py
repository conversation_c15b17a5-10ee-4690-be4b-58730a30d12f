from .character import Character
from .attributes import Attributes
from .skill import Skill
from .battle import Battle
from .item import Item, Equipment
from .inventory import Inventory
from .quest import Quest, QuestProgress
from .achievement import Achievement, AchievementProgress

def init_db():
    """初始化所有数据库表"""
    Character.create_table()
    Attributes.create_table()
    Skill.create_table()
    Battle.create_table()
    Item.create_table()
    Equipment.create_table()
    Inventory.create_table()
    Quest.create_table()
    QuestProgress.create_table()
    Achievement.create_table()
    AchievementProgress.create_table()