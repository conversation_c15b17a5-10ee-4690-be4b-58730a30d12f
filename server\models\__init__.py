from .user import User
from .character import Character
from .attributes import Attributes
from .skill import Skill
from .battle import Battle
from .item import Item, Equipment
from .inventory import Inventory
from .quest import Quest, QuestProgress
from .achievement import Achievement, AchievementProgress
from .monster import Monster
from .enemy import Enemy

def init_db():
    """初始化所有数据库表"""
    User.create_table()
    Character.create_table()
    Attributes.create_table()
    Skill.create_table()
    Battle.create_table()
    Item.create_table()
    Equipment.create_table()
    Inventory.create_table()
    Quest.create_table()
    QuestProgress.create_table()
    Achievement.create_table()
    AchievementProgress.create_table()
    Monster.create_table()
    Enemy.create_table()