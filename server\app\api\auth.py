from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2PasswordRequestForm, OAuth2PasswordBearer
from datetime import datetime, timedelta
from server.models.user import User
from server.core.auth import Auth<PERSON>anager, create_access_token
from pydantic import BaseModel
import jwt
from server.config import JWT_CONFIG

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/login")

router = APIRouter(tags=["认证"])

# JWT配置
SECRET_KEY = JWT_CONFIG.secret_key
ALGORITHM = JWT_CONFIG.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = JWT_CONFIG.access_token_expire_minutes

class UserRegister(BaseModel):
    phone: str
    password: str
    nickname: str = None

class Token(BaseModel):
    access_token: str
    token_type: str



@router.post("/register", response_model=Token)
async def register(user_data: UserRegister):
    """用户注册"""
    user_model = User()

    # 检查手机号是否已存在
    if user_model.get_user_by_phone(user_data.phone):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号已被注册"
        )

    # 创建用户
    user = user_model.create_user(
        phone=user_data.phone,
        password=user_data.password,
        nickname=user_data.nickname
    )

    # 生成访问令牌
    access_token = AuthManager.create_user_token(user)

    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """用户登录"""
    user = AuthManager.authenticate_user(form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = AuthManager.create_user_token(user)

    return {"access_token": access_token, "token_type": "bearer"}