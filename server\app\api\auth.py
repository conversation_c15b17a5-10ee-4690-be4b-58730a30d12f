from fastapi import APIRouter, Depends, HTTPException, status, Security
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from datetime import datetime, timedelta
from ...models.user import User
from pydantic import BaseModel
import jwt
from ...config import DATABASE_CONFIG, JWT_CONFIG

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """获取当前认证用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        phone: str = payload.get("sub")
        if phone is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    user_model = User()
    user = user_model.get_user_by_phone(phone)
    if user is None:
        raise credentials_exception
    return user

router = APIRouter(tags=["认证"])

# JWT配置
SECRET_KEY = JWT_CONFIG.secret_key
ALGORITHM = JWT_CONFIG.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = JWT_CONFIG.access_token_expire_minutes

class UserRegister(BaseModel):
    phone: str
    password: str
    nickname: str = None

class Token(BaseModel):
    access_token: str
    token_type: str

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建JWT令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@router.post("/register", response_model=Token)
async def register(user_data: UserRegister):
    """用户注册"""
    user_model = User()
    
    # 检查手机号是否已存在
    if user_model.get_user_by_phone(user_data.phone):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号已被注册"
        )
    
    # 创建用户
    user = user_model.create_user(
        phone=user_data.phone,
        password=user_data.password,
        nickname=user_data.nickname
    )
    
    # 生成访问令牌
    access_token = create_access_token(
        data={"sub": user["phone"]},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """用户登录"""
    user_model = User()
    user = user_model.get_user_by_phone(form_data.username)
    
    if not user or not user_model.verify_password(form_data.password, user["password"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = create_access_token(
        data={"sub": user["phone"]},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    return {"access_token": access_token, "token_type": "bearer"}