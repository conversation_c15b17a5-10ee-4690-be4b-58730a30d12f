"""
游戏核心逻辑模块
包含经验值计算、等级提升、技能效果等核心游戏机制
"""

import random
from typing import Dict, Any, List, Optional
from server.models.character import Character
from server.models.attributes import Attributes
from server.models.achievement import Achievement, AchievementProgress
from server.models.quest import Quest, QuestProgress
from server.core.db import db

class GameLogic:
    """游戏逻辑管理器"""
    
    @staticmethod
    def calculate_level_exp(level: int) -> int:
        """计算升级所需经验值"""
        return level * 100 + (level - 1) * 50
    
    @staticmethod
    def check_level_up(character_id: int) -> Dict[str, Any]:
        """检查并处理角色升级"""
        character = Character.get_by_id(character_id)
        if not character:
            return {"level_up": False}
        
        current_level = character[3]  # level
        current_exp = character[4]    # exp
        required_exp = GameLogic.calculate_level_exp(current_level + 1)
        
        if current_exp >= required_exp:
            # 升级
            new_level = current_level + 1
            remaining_exp = current_exp - required_exp
            
            # 更新角色等级和经验
            conn, cursor = db.get_db()
            cursor.execute('''
                UPDATE character 
                SET level = ?, exp = ?, max_hp = max_hp + 10, max_mp = max_mp + 5
                WHERE id = ?
            ''', (new_level, remaining_exp, character_id))
            conn.commit()
            
            # 检查成就
            GameLogic.check_achievements(character_id, "level_up", new_level)
            
            return {
                "level_up": True,
                "new_level": new_level,
                "remaining_exp": remaining_exp,
                "hp_bonus": 10,
                "mp_bonus": 5
            }
        
        return {"level_up": False}
    
    @staticmethod
    def check_achievements(character_id: int, action_type: str, value: int):
        """检查并更新成就进度"""
        conn, cursor = db.get_db()
        
        # 获取相关成就
        cursor.execute('''
            SELECT * FROM achievements 
            WHERE condition_type = ?
        ''', (action_type,))
        achievements = cursor.fetchall()
        
        for achievement in achievements:
            achievement_id = achievement[0]
            required_value = achievement[5]  # condition_value
            
            # 更新进度
            AchievementProgress.update_progress(character_id, achievement_id, value)
            
            # 检查是否达成
            if value >= required_value:
                AchievementProgress.unlock_achievement(character_id, achievement_id)
    
    @staticmethod
    def apply_skill_effect(character_id: int, skill_id: int, target_id: Optional[int] = None) -> Dict[str, Any]:
        """应用技能效果"""
        from server.models.skill import Skill
        
        skill = Skill.get_by_id(skill_id)
        if not skill:
            return {"success": False, "message": "技能不存在"}
        
        character = Character.get_by_id(character_id)
        if not character:
            return {"success": False, "message": "角色不存在"}
        
        # 检查MP是否足够
        current_mp = character[6]  # mp
        skill_cost = skill[4]      # mana_cost
        
        if current_mp < skill_cost:
            return {"success": False, "message": "MP不足"}
        
        # 扣除MP
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE character SET mp = mp - ? WHERE id = ?
        ''', (skill_cost, character_id))
        conn.commit()
        
        # 应用技能效果（简化版本）
        effect = skill[3]  # effect
        result = {"success": True, "effect": effect, "mp_cost": skill_cost}
        
        if "heal" in effect.lower():
            # 治疗技能
            heal_amount = 30 + random.randint(1, 20)
            cursor.execute('''
                UPDATE character 
                SET hp = MIN(max_hp, hp + ?) 
                WHERE id = ?
            ''', (heal_amount, character_id))
            conn.commit()
            result["heal_amount"] = heal_amount
        
        elif "attack" in effect.lower() and target_id:
            # 攻击技能
            damage = 20 + random.randint(1, 30)
            result["damage"] = damage
            result["target_id"] = target_id
        
        return result
    
    @staticmethod
    def complete_quest_objective(character_id: int, objective_type: str, value: int):
        """完成任务目标"""
        conn, cursor = db.get_db()
        
        # 获取角色进行中的任务
        cursor.execute('''
            SELECT qp.*, q.* FROM quest_progress qp
            JOIN quests q ON qp.quest_id = q.id
            WHERE qp.character_id = ? AND qp.status = 0
        ''', (character_id,))
        
        active_quests = cursor.fetchall()
        completed_quests = []
        
        for quest_progress in active_quests:
            quest_id = quest_progress[2]  # quest_id
            # 简化的任务完成逻辑
            # 实际游戏中需要更复杂的任务目标系统
            
            # 假设所有任务都可以通过特定行为完成
            if objective_type in ["kill_monster", "collect_item", "level_up"]:
                # 完成任务
                QuestProgress.complete_quest(character_id, quest_id)
                
                # 给予奖励
                reward_exp = quest_progress[16]    # reward_exp
                reward_gold = quest_progress[17]   # reward_gold
                
                if reward_exp > 0:
                    cursor.execute('''
                        UPDATE character SET exp = exp + ? WHERE id = ?
                    ''', (reward_exp, character_id))
                
                if reward_gold > 0:
                    cursor.execute('''
                        UPDATE character SET gold = gold + ? WHERE id = ?
                    ''', (reward_gold, character_id))
                
                conn.commit()
                
                completed_quests.append({
                    "quest_id": quest_id,
                    "name": quest_progress[13],  # quest name
                    "reward_exp": reward_exp,
                    "reward_gold": reward_gold
                })
                
                # 检查升级
                level_up_result = GameLogic.check_level_up(character_id)
                if level_up_result["level_up"]:
                    completed_quests[-1]["level_up"] = level_up_result
        
        return completed_quests
    
    @staticmethod
    def calculate_battle_damage(attacker_stats: Dict, defender_stats: Dict, skill_bonus: int = 0) -> int:
        """计算战斗伤害"""
        base_attack = attacker_stats.get("attack", 10)
        defense = defender_stats.get("defense", 5)
        
        # 基础伤害计算
        base_damage = base_attack + skill_bonus + random.randint(1, 10)
        
        # 防御减免
        final_damage = max(1, base_damage - defense)
        
        # 暴击判定
        critical_rate = attacker_stats.get("critical_rate", 5)
        if random.randint(1, 100) <= critical_rate:
            final_damage = int(final_damage * 1.5)
        
        return final_damage
    
    @staticmethod
    def get_character_combat_stats(character_id: int) -> Dict[str, int]:
        """获取角色战斗属性"""
        character = Character.get_by_id(character_id)
        attributes = Attributes.get_by_character_id(character_id)
        
        if not character or not attributes:
            return {}
        
        # 计算战斗属性
        derived_stats = Attributes.calculate_derived_stats(attributes)
        
        return {
            "hp": character[5],
            "max_hp": character[5],  # 应该从derived_stats获取
            "mp": character[6],
            "max_mp": character[6],  # 应该从derived_stats获取
            "attack": derived_stats["attack_power"],
            "defense": derived_stats["defense"],
            "critical_rate": derived_stats["critical_rate"],
            "dodge_rate": derived_stats["dodge_rate"]
        }
