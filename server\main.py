from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
import sqlite3
from contextlib import asynccontextmanager
from server.config import DATABASE_CONFIG, API_CONFIG
from server.models import init_db as init_database
from server.app.api.auth import router as auth_router
from server.app.api.character import router as character_router
from server.app.api.item import router as item_router
from server.app.api.battle import router as battle_router
from server.app.api.attributes import router as attributes_router
from server.app.api.skill import router as skill_router
from server.app.api.quest import router as quest_router
from server.app.api.achievement import router as achievement_router
from datetime import datetime, timedelta
import jwt
from fastapi.security import OAuth2PasswordBearer

# JWT配置
from server.config import JWT_CONFIG
SECRET_KEY = JWT_CONFIG.secret_key
ALGORITHM = JWT_CONFIG.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = JWT_CONFIG.access_token_expire_minutes

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/login")

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化数据库
    init_database()
    yield
    # 关闭时清理资源
    pass

# 创建FastAPI应用
app = FastAPI(
    lifespan=lifespan,
    title="末日觉醒MUD游戏API",
    description="末日觉醒MUD游戏的后端API文档",
    version="0.1.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(auth_router, prefix="/api/v1")
app.include_router(character_router, prefix="/api/v1")
app.include_router(item_router, prefix="/api/v1")
app.include_router(battle_router, prefix="/api/v1")
app.include_router(attributes_router, prefix="/api/v1") 
app.include_router(skill_router, prefix="/api/v1")
app.include_router(quest_router, prefix="/api/v1")
app.include_router(achievement_router, prefix="/api/v1")

# 第一个测试API
@app.get("/")
async def root():
    return {"message": "末日觉醒MUD游戏服务器已启动"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=API_CONFIG['api'].host, port=API_CONFIG['api'].port,debug=True)