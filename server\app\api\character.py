from fastapi import APIRouter, Depends, HTTPException
from server.models.character import Character
from server.core.auth import get_current_user
from server.models.user import User
from typing import Dict, Any

router = APIRouter(prefix="/api/v1/characters", tags=["characters"])

@router.post("/create")
async def create_character(
    name: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """创建新角色"""
    if not name or len(name) > 16:
        raise HTTPException(status_code=400, detail="角色名称长度1-16字符")

    character_id = Character.create(current_user["id"], name)
    return {"code": 200, "data": {"character_id": character_id}}

@router.get("/")
async def get_characters(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取用户的所有角色"""
    characters = Character.get_by_user_id(current_user["id"])
    return {"code": 200, "data": characters}

@router.get("/{character_id}")
async def get_character(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取特定角色详情"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权访问该角色")

    return {"code": 200, "data": character}

@router.get("/list")
async def get_character_list(
    current_user: dict = Depends(get_current_user)
):
    """获取用户角色列表"""
    characters = Character.get_by_user(current_user["id"])
    return {"code": 200, "data": characters}

@router.post("/select/{character_id}")
async def select_character(
    character_id: int,
    current_user: dict = Depends(get_current_user)
):
    """选择当前角色"""
    character = Character.get_by_id(character_id)
    if not character or character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    User.update_current_character(current_user["id"], character_id)
    return {"code": 200, "data": character}