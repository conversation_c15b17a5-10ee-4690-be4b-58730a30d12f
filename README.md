# 末日觉醒 MUD 游戏

一个基于FastAPI的文字冒险游戏后端系统，支持角色创建、战斗、技能、任务、成就等完整的游戏功能。

## 🎮 游戏特色

- **完整的角色系统**: 创建角色、属性分配、等级提升
- **战斗系统**: 回合制战斗，支持技能和装备
- **任务系统**: 主线任务、支线任务、日常任务
- **成就系统**: 丰富的成就奖励机制
- **物品装备**: 装备系统、背包管理
- **技能学习**: 多样化的技能效果

## 🚀 快速开始

### 环境要求

- Python 3.8+
- SQLite 3 (Python内置)

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动服务器

使用启动脚本（推荐）:
```bash
python start_server.py
```

或手动启动:
```bash
python -m uvicorn server.main:app --reload --host 0.0.0.0 --port 8000
```

### 初始化游戏数据

```bash
python server/scripts/init_game_data.py
```

## 📚 API 文档

启动服务器后访问: http://localhost:8000/docs

### 主要API端点

#### 用户认证
- `POST /api/v1/register` - 用户注册
- `POST /api/v1/login` - 用户登录

#### 角色管理
- `POST /api/v1/characters/create` - 创建角色
- `GET /api/v1/characters/` - 获取角色列表
- `GET /api/v1/characters/{id}` - 获取角色详情

#### 战斗系统
- `POST /api/v1/battle/start` - 开始战斗
- `POST /api/v1/battle/round` - 执行战斗回合

#### 属性系统
- `GET /api/v1/attributes/character/{id}` - 获取角色属性
- `POST /api/v1/attributes/character/{id}/increase` - 提升属性

#### 物品装备
- `GET /api/v1/items/` - 获取所有物品
- `GET /api/v1/items/character/{id}/inventory` - 获取角色背包
- `POST /api/v1/items/character/{id}/equip/{item_id}` - 装备物品

#### 技能系统
- `GET /api/v1/skills/` - 获取所有技能
- `GET /api/v1/skills/character/{id}/learned` - 获取已学技能
- `POST /api/v1/skills/character/{id}/learn/{skill_id}` - 学习技能

#### 任务系统
- `GET /api/v1/quests/` - 获取所有任务
- `GET /api/v1/quests/character/{id}` - 获取角色任务
- `POST /api/v1/quests/character/{id}/start/{quest_id}` - 开始任务

#### 成就系统
- `GET /api/v1/achievements/` - 获取所有成就
- `GET /api/v1/achievements/character/{id}` - 获取角色成就状态

## 🧪 运行测试

运行所有测试:
```bash
python run_tests.py
```

或使用pytest:
```bash
pytest tests/ -v
```

生成测试覆盖率报告:
```bash
pytest tests/ --cov=server --cov-report=html
```

## 📁 项目结构

```
├── server/                 # 服务器代码
│   ├── app/               # FastAPI应用
│   │   └── api/           # API路由
│   ├── core/              # 核心模块
│   │   ├── auth.py        # 认证模块
│   │   ├── db.py          # 数据库连接
│   │   └── game_logic.py  # 游戏逻辑
│   ├── models/            # 数据模型
│   ├── scripts/           # 初始化脚本
│   ├── config.py          # 配置管理
│   └── main.py            # 应用入口
├── tests/                 # 测试文件
├── client/                # 客户端代码（预留）
├── demo/                  # 演示代码
├── data/                  # 游戏数据
├── readme/                # 设计文档
├── .env                   # 环境配置
├── requirements.txt       # 依赖列表
├── start_server.py        # 启动脚本
└── run_tests.py          # 测试脚本
```

## 🎯 游戏数据

### 怪物列表
1. 小史莱姆 (等级1) - 新手怪物
2. 哥布林 (等级2) - 基础敌人
3. 骷髅兵 (等级3) - 亡灵生物
4. 巨魔 (等级5) - 强力怪物
5. 暗影狼 (等级4) - 敏捷型敌人
6. 火焰蜘蛛 (等级6) - 魔法攻击
7. 冰霜巨人 (等级8) - 高防御
8. 末日使者 (等级10) - BOSS级

### 装备类型
- **武器**: 剑、法杖、弓箭
- **防具**: 头盔、胸甲、护手、护腿、靴子
- **饰品**: 戒指、项链

### 技能分类
- **战士技能**: 重击、防御姿态、狂暴
- **法师技能**: 火球术、冰霜箭、雷电术
- **治疗技能**: 治疗术、群体治疗

## 🔧 配置说明

### 环境变量 (.env)

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mud_game
DB_USER=postgres
DB_PASSWORD=password

# JWT配置
SECRET_KEY=your-secret-key-32-chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 游戏配置
MAX_CHARACTERS_PER_USER=3
STARTING_LEVEL=1
STARTING_GOLD=100

# 调试模式
DEBUG_MODE=true
ENVIRONMENT=development
```

## 🚀 部署

### 开发环境
```bash
python start_server.py
```

### 生产环境
```bash
gunicorn server.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "server.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目链接: [https://github.com/yourusername/mud-game](https://github.com/yourusername/mud-game)
- 问题反馈: [Issues](https://github.com/yourusername/mud-game/issues)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！