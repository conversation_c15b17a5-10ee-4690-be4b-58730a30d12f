# 末日觉醒 MUD 游戏

## 战斗系统API

### 开始战斗
`POST /api/v1/battle/start`
参数:
- monster_id: 怪物ID

请求示例:
```json
{
    "monster_id": 1
}
```

返回示例:
```json
{
    "battle_id": "1_1",
    "character": {
        "id": 1,
        "current_hp": 100,
        "max_hp": 100
    },
    "monster": {
        "id": 1,
        "name": "小史莱姆",
        "hp": 50,
        "attack": 5,
        "defense": 2
    },
    "message": "战斗开始"
}
```

### 战斗回合
`POST /api/v1/battle/round`
参数:
- battle_id: 战斗ID

请求示例:
```json
{
    "battle_id": "1_1"
}
```

返回示例:
```json
{
    "battle_over": false,
    "winner": null,
    "round": 1,
    "round_result": {
        "damage_to_monster": 15,
        "damage_to_character": 8,
        "character_hp_left": 92,
        "monster_hp_left": 35
    }
}
```

### 战斗结束
当战斗结束时，返回示例:
```json
{
    "battle_over": true,
    "winner": "character",
    "round_result": {
        "damage_to_monster": 20,
        "damage_to_character": 5,
        "character_hp_left": 87,
        "monster_hp_left": 0
    },
    "exp_gained": 10
}
```

### 初始化怪物数据
运行脚本初始化怪物数据:
```bash
python server/scripts/init_monsters.py
```

### 可用怪物列表
1. 小史莱姆 (ID:1)
2. 哥布林 (ID:2)
3. 骷髅兵 (ID:3)
4. 巨魔 (ID:4)