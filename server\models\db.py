import sqlite3
from pathlib import Path

class Database:
    def __init__(self):
        self.conn = None
        self.cursor = None
        
    def get_db(self):
        """获取数据库连接"""
        if not self.conn:
            db_path = Path(__file__).parent.parent.parent / "game.db"
            self.conn = sqlite3.connect(str(db_path))
            self.cursor = self.conn.cursor()
        return self.conn, self.cursor

db = Database()