from server.core.db import db

class Quest:
    """任务系统"""
    def __init__(self):
        self.table_name = "quests"
    
    @staticmethod
    def create_table():
        """创建任务表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                type INTEGER NOT NULL,  -- 1=主线, 2=支线, 3=日常
                min_level INTEGER DEFAULT 1,
                repeatable BOOLEAN DEFAULT FALSE,
                reward_exp INTEGER DEFAULT 0,
                reward_gold INTEGER DEFAULT 0,
                reward_item_id INTEGER,
                reward_item_quantity INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()

    @staticmethod
    def get_all():
        """获取所有任务"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM quests')
        return cursor.fetchall()

    @staticmethod
    def get_by_id(quest_id):
        """根据ID获取任务"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM quests WHERE id = ?', (quest_id,))
        return cursor.fetchone()

    @staticmethod
    def create(name, description, quest_type=1, min_level=1, repeatable=False,
               reward_exp=0, reward_gold=0, reward_item_id=None, reward_item_quantity=1):
        """创建新任务"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO quests
            (name, description, type, min_level, repeatable, reward_exp, reward_gold, reward_item_id, reward_item_quantity)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, description, quest_type, min_level, repeatable, reward_exp, reward_gold, reward_item_id, reward_item_quantity))
        conn.commit()
        return cursor.lastrowid

class QuestProgress:
    """任务进度"""
    def __init__(self):
        self.table_name = "quest_progress"

    @staticmethod
    def create_table():
        """创建任务进度表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quest_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                character_id INTEGER NOT NULL,
                quest_id INTEGER NOT NULL,
                status INTEGER DEFAULT 0,  -- 0=进行中, 1=已完成, 2=已失败
                progress INTEGER DEFAULT 0,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                FOREIGN KEY (character_id) REFERENCES character(id),
                FOREIGN KEY (quest_id) REFERENCES quests(id),
                UNIQUE(character_id, quest_id)
            )
        ''')
        conn.commit()

    @staticmethod
    def start_quest(character_id, quest_id):
        """开始任务"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT OR IGNORE INTO quest_progress
            (character_id, quest_id, status, progress)
            VALUES (?, ?, 0, 0)
        ''', (character_id, quest_id))
        conn.commit()
        return cursor.rowcount > 0

    @staticmethod
    def complete_quest(character_id, quest_id):
        """完成任务"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE quest_progress
            SET status = 1, completed_at = CURRENT_TIMESTAMP
            WHERE character_id = ? AND quest_id = ?
        ''', (character_id, quest_id))
        conn.commit()
        return cursor.rowcount > 0

    @staticmethod
    def get_character_quests(character_id):
        """获取角色的任务进度"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT qp.*, q.name, q.description, q.type, q.reward_exp, q.reward_gold
            FROM quest_progress qp
            JOIN quests q ON qp.quest_id = q.id
            WHERE qp.character_id = ?
        ''', (character_id,))
        return cursor.fetchall()

    @staticmethod
    def get_by_id(quest_id):
        """根据ID获取任务"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM quests WHERE id = ?', (quest_id,))
        return cursor.fetchone()

class QuestProgress:
    """任务进度跟踪"""
    def __init__(self):
        self.table_name = "quest_progress"
    
    @staticmethod
    def create_table():
        """创建任务进度表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quest_progress (
                character_id INTEGER NOT NULL,
                quest_id INTEGER NOT NULL,
                status INTEGER DEFAULT 0,  -- 0=未接受, 1=进行中, 2=已完成, 3=已领取奖励
                progress INTEGER DEFAULT 0,
                completed_at TIMESTAMP,
                PRIMARY KEY (character_id, quest_id),
                FOREIGN KEY (character_id) REFERENCES characters(id),
                FOREIGN KEY (quest_id) REFERENCES quests(id)
            )
        ''')
        conn.commit()

    @staticmethod
    def start_quest(character_id, quest_id):
        """开始任务"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT OR IGNORE INTO quest_progress 
            (character_id, quest_id, status)
            VALUES (?, ?, 1)
        ''', (character_id, quest_id))
        conn.commit()

    @staticmethod
    def update_progress(character_id, quest_id, progress):
        """更新任务进度"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE quest_progress 
            SET progress = ?
            WHERE character_id = ? AND quest_id = ? AND status = 1
        ''', (progress, character_id, quest_id))
        conn.commit()

    @staticmethod
    def complete_quest(character_id, quest_id):
        """完成任务"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE quest_progress 
            SET status = 2, completed_at = CURRENT_TIMESTAMP
            WHERE character_id = ? AND quest_id = ? AND status = 1
        ''', (character_id, quest_id))
        conn.commit()

    @staticmethod
    def claim_reward(character_id, quest_id):
        """领取任务奖励"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE quest_progress 
            SET status = 3
            WHERE character_id = ? AND quest_id = ? AND status = 2
        ''', (character_id, quest_id))
        conn.commit()

    @staticmethod
    def get_character_quests(character_id):
        """获取角色的所有任务状态"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT q.*, qp.status, qp.progress, qp.completed_at
            FROM quests q
            LEFT JOIN quest_progress qp ON q.id = qp.quest_id AND qp.character_id = ?
        ''', (character_id,))
        return cursor.fetchall()