from server.core.db import db
from server.models.item import Item, Equipment, EquipmentSlot

class Inventory:
    """背包系统"""
    def __init__(self):
        self.table_name = "inventories"
    
    @staticmethod
    def create_table():
        """创建背包表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventories (
                character_id INTEGER NOT NULL,
                item_id INTEGER NOT NULL,
                quantity INTEGER DEFAULT 1,
                equipped BOOLEAN DEFAULT FALSE,
                PRIMARY KEY (character_id, item_id),
                FOREIGN KEY (character_id) REFERENCES characters(id),
                FOREIGN KEY (item_id) REFERENCES items(id)
            )
        ''')
        conn.commit()

    @staticmethod
    def add_item(character_id, item_id, quantity=1):
        """添加物品到背包"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT OR IGNORE INTO inventories 
            (character_id, item_id, quantity)
            VALUES (?, ?, ?)
        ''', (character_id, item_id, quantity))
        
        cursor.execute('''
            UPDATE inventories 
            SET quantity = quantity + ?
            WHERE character_id = ? AND item_id = ?
        ''', (quantity, character_id, item_id))
        
        conn.commit()

    @staticmethod
    def remove_item(character_id, item_id, quantity=1):
        """从背包移除物品"""
        conn, cursor = db.get_db()
        cursor.execute('''
            UPDATE inventories 
            SET quantity = quantity - ?
            WHERE character_id = ? AND item_id = ? AND quantity >= ?
        ''', (quantity, character_id, item_id, quantity))
        
        cursor.execute('''
            DELETE FROM inventories 
            WHERE character_id = ? AND item_id = ? AND quantity <= 0
        ''', (character_id, item_id))
        
        conn.commit()

    @staticmethod
    def equip_item(character_id, item_id):
        """装备物品"""
        item = Item.get_by_id(item_id)
        if not item or item['type'] != 1:  # 1 = Equipment
            return False
        
        conn, cursor = db.get_db()
        # 先卸下同槽位装备
        cursor.execute('''
            UPDATE inventories 
            SET equipped = FALSE
            WHERE character_id = ? AND item_id IN (
                SELECT e.id 
                FROM equipment e
                JOIN inventories i ON e.id = i.item_id
                WHERE i.character_id = ? AND e.slot = (
                    SELECT slot FROM equipment WHERE id = ?
                )
            )
        ''', (character_id, character_id, item_id))
        
        # 装备新物品
        cursor.execute('''
            UPDATE inventories 
            SET equipped = TRUE
            WHERE character_id = ? AND item_id = ?
        ''', (character_id, item_id))
        
        conn.commit()
        return True

    @staticmethod
    def get_character_inventory(character_id):
        """获取角色背包内容"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT i.*, it.name, it.type, it.description, it.value,
                   e.slot, e.attack_bonus, e.defense_bonus, e.hp_bonus, e.durability
            FROM inventories i
            JOIN items it ON i.item_id = it.id
            LEFT JOIN equipment e ON i.item_id = e.id
            WHERE i.character_id = ?
        ''', (character_id,))
        return cursor.fetchall()