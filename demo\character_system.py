from enum import Enum
from typing import Dict, List, Optional
from item_system import Item, ItemType
import random

class CharacterClass(Enum):
    """角色职业枚举"""
    SURVIVOR = "生存专家"  # 均衡型，擅长资源收集
    SOLDIER = "退役士兵"   # 高攻击，擅长战斗
    DOCTOR = "医疗专家"    # 高防御，擅长治疗
    ENGINEER = "机械专家"  # 高智力，擅长建造

class CharacterAttributes:
    """角色属性类"""
    
    def __init__(self):
        # 基础属性
        self.level = 1 # 等级
        self.exp = 0 # 经验值
        self.gold = 0 # 金币
        self.hp = 100 # 当前生命值
        self.max_hp = 100 # 最大生命值
        self.mp = 50 # 当前魔法值
        self.max_mp = 50 # 最大魔法值
        
        # 战斗属性
        self.attack = 10 # 基础攻击力
        self.defense = 10 # 基础防御力
        self.intelligence = 10 # 智力
        self.agility = 10 # 敏捷
        self.crit_rate = 0.05 # 暴击率(5%)
        self.dodge_rate = 0.1 # 闪避率(10%)
        self.hit_rate = 0.9 # 命中率(90%)
        
        # 战斗状态
        self.buffs = [] # 增益效果
        self.debuffs = [] # 减益效果
        
        # 状态效果抵抗和免疫
        self.effect_resist = {
            "poison": 0.0,    # 中毒抵抗率
            "stun": 0.0,      # 眩晕抵抗率 
            "silence": 0.0,   # 沉默抵抗率
            "buff": 0.0       # 增益效果抵抗率(对敌方)
        }
        self.effect_immunity = {
            "poison": False,  # 是否免疫中毒
            "stun": False,    # 是否免疫眩晕
            "silence": False  # 是否免疫沉默
        }
        
        # 成长系数（根据职业不同而变化）
        self.growth_rates = {
            'hp': 1.0,
            'attack': 1.0,
            'defense': 1.0,
            'intelligence': 1.0,
            'agility': 1.0
        }
        
    def level_up(self):
        """升级角色"""
        self.level += 1
        self.max_hp = int(self.max_hp * (1 + 0.1 * self.growth_rates['hp']))
        self.hp = self.max_hp
        self.attack = int(self.attack * (1 + 0.1 * self.growth_rates['attack']))
        self.defense = int(self.defense * (1 + 0.1 * self.growth_rates['defense']))
        self.intelligence = int(self.intelligence * (1 + 0.1 * self.growth_rates['intelligence']))
        self.agility = int(self.agility * (1 + 0.1 * self.growth_rates['agility']))
        
    def take_damage(self, damage: int) -> int:
        """承受伤害并返回实际伤害值"""
        defense_factor = self.growth_rates.get('defense', 1.0)
        actual_damage = max(1, damage - int(self.defense * defense_factor * 0.05))
        self.hp = max(0, self.hp - actual_damage)
        return actual_damage

class StatusEffect:
    """状态效果基类"""
    def __init__(self, name: str, duration: int):
        self.name = name
        self.duration = duration
        self.stacks = 1
    
    def apply(self, character):
        """应用效果到角色"""
        pass
        
    def remove(self, character):
        """移除效果"""
        pass
        
    def update(self, character):
        """每回合更新"""
        self.duration -= 1
        return self.duration > 0
        
    def __str__(self):
        return f"{self.name}({self.duration}回合)"

class CharacterSkill:
    """角色技能类"""
    
    def __init__(self, name: str, description: str, effect: Dict, level_required: int, mp_cost: int = 10):
        self.name = name
        self.description = description
        # 效果结构: {"type": "damage/heal", "value": 10, "effects": [StatusEffect]}
        self.effect = effect  
        self.level_required = level_required
        self.mp_cost = mp_cost  # MP消耗
        self.cooldown = 0  # 当前冷却回合数
        self.max_cooldown = 3  # 最大冷却回合数
        
    def apply_effects(self, target):
        """应用技能附带的状态效果"""
        if "effects" in self.effect:
            for effect in self.effect["effects"]:
                target.add_status_effect(effect)
                print(f"{target.name} 获得了 {effect.name} 效果!")
                
    def get_full_description(self):
        """获取完整的技能描述(包含状态效果)"""
        desc = f"{self.name}: {self.description}"
        if "effects" in self.effect:
            effects = ", ".join([str(e) for e in self.effect["effects"]])
            desc += f"\n附带效果: {effects}"
        return desc

class AttackBoostEffect(StatusEffect):
    """攻击力提升效果"""
    def __init__(self, amount: float, duration: int):
        super().__init__(f"攻击+{amount}", duration)
        self.amount = amount
        
    def apply(self, character):
        character.attributes.attack += self.amount
        
    def remove(self, character):
        character.attributes.attack -= self.amount

class PoisonEffect(StatusEffect):
    """中毒效果"""
    def __init__(self, damage: int, duration: int):
        super().__init__(f"中毒({damage})", duration)
        self.damage = damage
        
    def update(self, character):
        character.take_damage(self.damage)
        return super().update(character)

class Character:
    """角色主类"""
    
    def __init__(self, name: str, char_class: CharacterClass):
        self.name = name
        self.char_class = char_class
        self.attributes = CharacterAttributes()
        self.skills: List[CharacterSkill] = []
        self.inventory: Dict[int, Item] = {}  # 道具背包 {item_id: Item}
        self.status_effects: List[StatusEffect] = []  # 当前状态效果
        self._setup_initial_skills()
        self._apply_class_bonuses()

    def add_status_effect(self, effect: StatusEffect):
        """添加状态效果"""
        # 检查效果类型
        effect_type = effect.name.split('(')[0].lower()
        
        # 检查免疫
        if effect_type in self.attributes.effect_immunity and self.attributes.effect_immunity[effect_type]:
            print(f"{self.name} 免疫 {effect.name} 效果!")
            return
            
        # 检查抵抗
        resist_rate = self.attributes.effect_resist.get(effect_type, 0.0)
        if random.random() < resist_rate:
            print(f"{self.name} 抵抗了 {effect.name} 效果!")
            return
            
        # 检查是否已有相同效果
        existing = next((e for e in self.status_effects if e.name == effect.name), None)
        if existing:
            existing.stacks += 1
            existing.duration = max(existing.duration, effect.duration)
            print(f"{self.name} 的 {effect.name} 效果叠加到 {existing.stacks}层!")
        else:
            effect.apply(self)
            self.status_effects.append(effect)
            print(f"{self.name} 获得了 {effect.name} 效果!")
            
    def remove_status_effect(self, effect_name: str):
        """移除状态效果"""
        effect = next((e for e in self.status_effects if e.name == effect_name), None)
        if effect:
            effect.remove(self)
            self.status_effects.remove(effect)
            
    def update_status_effects(self):
        """更新所有状态效果(每回合调用)"""
        for effect in self.status_effects[:]:  # 创建副本以便安全删除
            if not effect.update(self):
                effect.remove(self)
                self.status_effects.remove(effect)

    def show_commands(self):
        """显示所有可操作命令"""
        print("\n=== 角色操作命令 ===")
        print("\n[战斗命令]")
        print("1. attack(target) - 普通攻击")
        print("2. use_skill(skill_name) - 使用技能")
        print("\n[道具命令]")
        print("3. add_item(item) - 添加道具")
        print("4. remove_item(item_id[, amount]) - 移除道具")
        print("5. use_item(item_id) - 使用道具")
        print("6. get_item_count(item_id) - 查询道具数量")
        print("7. get_item(item_id) - 获取道具信息")
        print("\n[属性命令]")

        print("9. attributes.take_damage(damage) - 承受伤害")
        print("\n[系统命令]")
        print("10. help() - 显示此帮助信息")
        print("11. status() - 查看角色状态")
        print("====================\n")

    def attack(self, target):
        """普通攻击
        参数:
            target: 攻击目标(Character对象)
        返回:
            bool: 是否攻击成功
        """
        if not target or not hasattr(target, 'take_damage'):
            print("无效的目标")
            return False
            
        # 命中判定 (基础命中率 + 攻击者敏捷 - 目标敏捷)
        hit_chance = min(0.95, max(0.05, 
            self.attributes.hit_rate + 
            (self.attributes.agility - target.attributes.agility) * 0.01))
            
        if random.random() > hit_chance:
            print(f"{self.name} 的攻击被 {target.name} 闪避了!")
            return False
            
        # 基础伤害计算 (攻击力 * 1.5 - 目标防御 * 0.3)
        base_damage = max(1, 
            self.attributes.attack * 1.5 - 
            target.attributes.defense * 0.3)
        
        # 暴击判定
        is_critical = random.random() < self.attributes.crit_rate
        damage = base_damage * (2 if is_critical else 1)
        
        # 实际伤害
        actual_damage = target.take_damage(damage)
        
        # 战斗反馈
        if is_critical:
            print(f"暴击! {self.name} 对 {target.name} 造成了 {actual_damage:.1f} 点伤害!")
        else:
            print(f"{self.name} 对 {target.name} 造成了 {actual_damage:.1f} 点伤害!")
            
        # 概率附加状态效果 (10%几率)
        if random.random() < 0.1:
            effect = PoisonEffect(damage=2, duration=3)
            target.add_status_effect(effect)
            print(f"{target.name} 中毒了! 将在3回合内受到持续伤害")
            
        return actual_damage > 0

    def status(self):
        """显示角色完整状态"""
        print(f"\n=== {self.name} 状态 ===")
        print(f"职业: {self.char_class.value}")
        print(f"等级: {self.attributes.level}")
        print(f"生命: {self.attributes.hp}/{self.attributes.max_hp}")
        print(f"魔法: {self.attributes.mp}/{self.attributes.max_mp}")
        print(f"攻击: {self.attributes.attack}")
        print(f"防御: {self.attributes.defense}")
        print(f"智力: {self.attributes.intelligence}")
        print(f"敏捷: {self.attributes.agility}")
        
        # 显示状态效果
        if self.status_effects:
            print("\n状态效果:")
            for effect in self.status_effects:
                print(f"- {effect}")
                
        print("====================\n")

    def help(self):
        """显示帮助信息"""
        self.show_commands()
        
    def _setup_initial_skills(self):
        """根据职业设置初始技能"""
        if self.char_class == CharacterClass.SURVIVOR:
            self.skills.append(CharacterSkill(
                "资源搜寻", "提高20%资源收集效率", {"type": "gather", "value": 1.2}, 1
            ))
        elif self.char_class == CharacterClass.SOLDIER:
            self.skills.append(CharacterSkill(
                "精准射击", "造成120%攻击力的伤害", {"type": "damage", "value": 1.2}, 1
            ))
        elif self.char_class == CharacterClass.DOCTOR:
            self.skills.append(CharacterSkill(
                "紧急包扎", "恢复50点生命值", {"type": "heal", "value": 50}, 1
            ))
        elif self.char_class == CharacterClass.ENGINEER:
            self.skills.append(CharacterSkill(
                "快速修理", "立即修复装备耐久度", {"type": "repair", "value": 100}, 1
            ))
    
    def _apply_class_bonuses(self):
        """应用职业成长系数"""
        if self.char_class == CharacterClass.SURVIVOR:
            self.attributes.growth_rates = {'hp': 1.1, 'attack': 1.0, 'defense': 1.0, 
                                         'intelligence': 1.1, 'agility': 1.1}
        elif self.char_class == CharacterClass.SOLDIER:
            self.attributes.growth_rates = {'hp': 1.2, 'attack': 1.3, 'defense': 1.1, 
                                         'intelligence': 0.8, 'agility': 1.0}
        elif self.char_class == CharacterClass.DOCTOR:
            self.attributes.growth_rates = {'hp': 1.3, 'attack': 0.8, 'defense': 1.2, 
                                         'intelligence': 1.2, 'agility': 0.9}
        elif self.char_class == CharacterClass.ENGINEER:
            self.attributes.growth_rates = {'hp': 0.9, 'attack': 0.9, 'defense': 1.1, 
                                         'intelligence': 1.3, 'agility': 1.0}

    def use_skill(self, skill_name: str, target=None) -> Dict:
        """使用技能
        参数:
            skill_name: 技能名称
            target: 技能目标(Character对象), 默认为None(自身)
        返回:
            Dict: 技能效果字典(空字典表示使用失败)
        """
        skill = next((s for s in self.skills if s.name == skill_name), None)
        if not skill:
            print(f"技能 {skill_name} 不存在")
            return {}
            
        # 检查等级要求
        if self.attributes.level < skill.level_required:
            print(f"需要等级 {skill.level_required} 才能使用此技能")
            return {}
            
        # 检查MP消耗
        if self.attributes.mp < skill.mp_cost:
            print(f"MP不足，需要 {skill.mp_cost} MP (当前: {self.attributes.mp})")
            return {}
            
        # 检查冷却时间
        if skill.cooldown > 0:
            print(f"技能冷却中，剩余 {skill.cooldown} 回合")
            return {}
            
        # 使用技能
        self.attributes.mp -= skill.mp_cost
        skill.cooldown = skill.max_cooldown
        print(f"成功使用 {skill_name}! 消耗 {skill.mp_cost} MP")
        
        # 应用技能效果
        result = skill.effect.copy()
        
        # 处理目标
        if target is None:
            target = self
        elif not hasattr(target, 'add_status_effect'):
            print(f"无效的目标: {target}")
            return {}
            
        # 应用状态效果(70%基础成功率)
        if "effects" in skill.effect and random.random() < 0.7:
            skill.apply_effects(target)
            result["effects_applied"] = True
        else:
            result["effects_applied"] = False
            
        return result

    def update_cooldowns(self):
        """更新技能冷却"""
        for skill in self.skills:
            if skill.cooldown > 0:
                skill.cooldown -= 1

    def add_item(self, item: Item) -> bool:
        """添加道具到背包
        参数:
            item: 要添加的道具对象
        返回:
            bool: 是否添加成功(False表示无效道具或背包已满)
        """
        if not isinstance(item, Item):
            return False
        if not hasattr(item, 'item_id'):
            return False
            
        existing_item = self.inventory.get(item.item_id)
        if existing_item:
            # 已有相同道具，尝试堆叠
            if (existing_item.name == item.name and 
                existing_item.quantity < existing_item.max_stack):
                existing_item.quantity += item.quantity
                return True
            return False
        else:
            # 新道具
            self.inventory[item.item_id] = item
            return True

    def remove_item(self, item_id: int, amount: int = 1) -> bool:
        """从背包移除道具
        参数:
            item_id: 要移除的道具ID
            amount: 要移除的数量(默认为1)
        返回:
            bool: 是否移除成功
        """
        if not isinstance(item_id, int) or item_id < 0 or amount < 1:
            return False
        if item_id not in self.inventory:
            return False
            
        item = self.inventory[item_id]
        if amount >= item.quantity:
            del self.inventory[item_id]
        else:
            item.quantity -= amount
        return True

    def use_item(self, item_id: int) -> bool:
        """使用道具
        参数:
            item_id: 要使用的道具ID
        返回:
            bool: 是否使用成功
        """
        if not isinstance(item_id, int) or item_id < 0:
            return False
        if item_id not in self.inventory:
            return False
        
        item = self.inventory[item_id]
        if not hasattr(item, 'use') or not callable(item.use):
            return False
            
        success = item.use(self)
        if success and hasattr(item, 'item_type') and item.item_type == ItemType.CONSUMABLE:
            self.remove_item(item_id)
        return success

    def get_item_count(self, item_id: int) -> int:
        """获取道具数量
        参数:
            item_id: 道具ID
        返回:
            int: 道具数量(不存在返回0)
        """
        if not isinstance(item_id, int) or item_id < 0:
            return 0
        item = self.inventory.get(item_id)
        return item.quantity if item else 0

    def get_item(self, item_id: int) -> Optional[Item]:
        """获取道具信息
        参数:
            item_id: 道具ID
        返回:
            Optional[Item]: 道具对象(不存在则返回None)
        """
        return self.inventory.get(item_id)

if __name__ == "__main__":
    # 测试角色系统
    print("测试角色系统:")
    survivor = Character("测试生存者", CharacterClass.SURVIVOR)
    print(f"{survivor.name} ({survivor.char_class.value}) 创建成功!")
    print(f"初始HP: {survivor.attributes.hp}, 攻击: {survivor.attributes.attack}")
    
    print("\n升级后:")
    survivor.attributes.level_up()
    print(f"等级: {survivor.attributes.level}")
    print(f"HP: {survivor.attributes.hp}/{survivor.attributes.max_hp}")
    print(f"攻击: {survivor.attributes.attack}")
    
    print("\n可用技能:")
    for skill in survivor.skills:
        print(f"- {skill.name}: {skill.description}")