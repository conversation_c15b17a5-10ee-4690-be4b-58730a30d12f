from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from server.models.achievement import Achievement, AchievementProgress
from server.models.character import Character
from server.core.auth import get_current_user

router = APIRouter(prefix="/api/v1/achievements", tags=["achievements"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@router.get("/")
async def get_all_achievements():
    """获取所有成就"""
    return {"code": 200, "data": Achievement.get_all()}

@router.get("/{achievement_id}")
async def get_achievement(achievement_id: int):
    """获取特定成就详情"""
    achievement = Achievement.get_by_id(achievement_id)
    if not achievement:
        raise HTTPException(status_code=404, detail="成就不存在")
    return {"code": 200, "data": achievement}

@router.get("/character/{character_id}")
async def get_character_achievements(
    character_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取角色的成就状态"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权访问该角色")

    achievements = AchievementProgress.get_character_achievements(character_id)
    return {"code": 200, "data": achievements}

@router.post("/character/{character_id}/claim/{achievement_id}")
async def claim_achievement_reward(
    character_id: int,
    achievement_id: int,
    current_user: dict = Depends(get_current_user)
):
    """领取成就奖励"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")

    AchievementProgress.claim_reward(character_id, achievement_id)
    return {"code": 200, "message": "奖励领取成功"}

@router.get("/character/{character_id}")
async def get_character_achievements(character_id: int, token: str = Depends(oauth2_scheme)):
    """获取角色的所有成就状态"""
    # 验证用户权限
    user = get_current_user(token)
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    return AchievementProgress.get_character_achievements(character_id)

@router.post("/character/{character_id}/claim/{achievement_id}")
async def claim_achievement_reward(character_id: int, achievement_id: int, token: str = Depends(oauth2_scheme)):
    """领取成就奖励"""
    user = get_current_user(token)
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    AchievementProgress.claim_reward(character_id, achievement_id)
    return {"message": "Achievement reward claimed"}