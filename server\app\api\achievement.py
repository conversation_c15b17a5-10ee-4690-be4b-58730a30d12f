from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from server.models.achievement import Achievement, AchievementProgress
from server.models.character import Character
from server.core.auth import get_current_user

router = APIRouter(prefix="/api/v1/achievements", tags=["achievements"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@router.get("/")
async def get_all_achievements():
    """获取所有成就"""
    return Achievement.get_all()

@router.get("/{achievement_id}")
async def get_achievement(achievement_id: int):
    """获取特定成就详情"""
    achievement = Achievement.get_by_id(achievement_id)
    if not achievement:
        raise HTTPException(status_code=404, detail="Achievement not found")
    return achievement

@router.get("/character/{character_id}")
async def get_character_achievements(character_id: int, token: str = Depends(oauth2_scheme)):
    """获取角色的所有成就状态"""
    # 验证用户权限
    user = get_current_user(token)
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    return AchievementProgress.get_character_achievements(character_id)

@router.post("/character/{character_id}/claim/{achievement_id}")
async def claim_achievement_reward(character_id: int, achievement_id: int, token: str = Depends(oauth2_scheme)):
    """领取成就奖励"""
    user = get_current_user(token)
    if not Character.is_owner(character_id, user["id"]):
        raise HTTPException(status_code=403, detail="Not your character")
    
    AchievementProgress.claim_reward(character_id, achievement_id)
    return {"message": "Achievement reward claimed"}