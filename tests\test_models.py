"""
数据模型测试
"""

import pytest
from server.models.user import User
from server.models.character import Character
from server.models.attributes import Attributes
from server.models.item import Item, Equipment, ItemType, EquipmentSlot
from server.models.skill import Skill
from server.models.monster import Monster
from server.models.enemy import Enemy
from server.models.quest import Quest, QuestProgress
from server.models.achievement import Achievement, AchievementProgress
from server.models.inventory import Inventory
from server.models.battle import Battle

class TestUserModel:
    """用户模型测试"""
    
    def test_create_user(self, test_db):
        """测试创建用户"""
        user_model = User()
        user = user_model.create_user(
            phone="13800138001",
            password="test123",
            nickname="测试用户"
        )
        
        assert user["phone"] == "13800138001"
        assert user["nickname"] == "测试用户"
        assert "id" in user
    
    def test_get_user_by_phone(self, test_db):
        """测试通过手机号获取用户"""
        user_model = User()
        # 创建用户
        created_user = user_model.create_user(
            phone="13800138002",
            password="test123",
            nickname="测试用户2"
        )
        
        # 获取用户
        user = user_model.get_user_by_phone("13800138002")
        assert user is not None
        assert user["phone"] == "13800138002"
        assert user["id"] == created_user["id"]
    
    def test_password_verification(self, test_db):
        """测试密码验证"""
        user_model = User()
        user = user_model.create_user(
            phone="13800138003",
            password="test123",
            nickname="测试用户3"
        )
        
        # 验证正确密码
        assert user_model.verify_password("test123", user["password"])
        
        # 验证错误密码
        assert not user_model.verify_password("wrong", user["password"])

class TestCharacterModel:
    """角色模型测试"""
    
    def test_create_character(self, test_db):
        """测试创建角色"""
        # 先创建用户
        user_model = User()
        user = user_model.create_user(
            phone="13800138004",
            password="test123",
            nickname="测试用户4"
        )
        
        # 创建角色
        character_id = Character.create(user["id"], "测试角色")
        assert character_id is not None
        
        # 获取角色
        character = Character.get_by_id(character_id)
        assert character is not None
        assert character[2] == "测试角色"  # name字段
    
    def test_get_character_by_user(self, test_db):
        """测试获取用户的角色"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138005",
            password="test123",
            nickname="测试用户5"
        )
        
        character_id = Character.create(user["id"], "测试角色2")
        
        # 获取用户的角色
        characters = Character.get_by_user_id(user["id"])
        assert len(characters) > 0
        assert characters[0][0] == character_id

class TestAttributesModel:
    """属性模型测试"""
    
    def test_create_default_attributes(self, test_db):
        """测试创建默认属性"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138006",
            password="test123",
            nickname="测试用户6"
        )
        character_id = Character.create(user["id"], "测试角色3")
        
        # 获取属性（创建角色时应该自动创建）
        attributes = Attributes.get_by_character_id(character_id)
        assert attributes is not None
        assert attributes["strength"] == 10
        assert attributes["agility"] == 10
    
    def test_increase_attribute(self, test_db):
        """测试提升属性"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138007",
            password="test123",
            nickname="测试用户7"
        )
        character_id = Character.create(user["id"], "测试角色4")
        
        # 提升力量
        success = Attributes.increase_attribute(character_id, "strength", 5)
        assert success
        
        # 验证属性变化
        attributes = Attributes.get_by_character_id(character_id)
        assert attributes["strength"] == 15

class TestItemModel:
    """物品模型测试"""
    
    def test_create_item(self, test_db):
        """测试创建物品"""
        item_id = Item.create("测试剑", ItemType.EQUIPMENT, "一把测试用的剑", 100)
        assert item_id is not None
        
        item = Item.get_by_id(item_id)
        assert item is not None
        assert item[1] == "测试剑"  # name字段
    
    def test_create_equipment(self, test_db):
        """测试创建装备"""
        # 创建基础物品
        item_id = Item.create("测试盔甲", ItemType.EQUIPMENT, "一件测试盔甲", 200)
        
        # 创建装备属性
        Equipment.create_equipment(
            item_id, 
            EquipmentSlot.CHEST, 
            attack=0, 
            defense=15, 
            hp=30
        )
        
        # 验证装备创建成功
        item = Item.get_by_id(item_id)
        assert item is not None

class TestSkillModel:
    """技能模型测试"""
    
    def test_create_skill(self, test_db):
        """测试创建技能"""
        skill_id = Skill.create(
            name="测试技能",
            description="一个测试技能",
            effect="造成伤害",
            mana_cost=10,
            cooldown=5,
            required_level=1
        )
        assert skill_id is not None
        
        skill = Skill.get_by_id(skill_id)
        assert skill is not None
        assert skill[1] == "测试技能"

class TestMonsterModel:
    """怪物模型测试"""
    
    def test_create_monster(self, test_db):
        """测试创建怪物"""
        monster_id = Monster.create(
            name="测试怪物",
            level=1,
            hp=50,
            attack=10,
            defense=5,
            exp_reward=15,
            item_drop_rate=0.1,
            drop_items=[1, 2]
        )
        assert monster_id is not None
        
        monster = Monster.get_by_id(monster_id)
        assert monster is not None
        assert monster[1] == "测试怪物"

class TestQuestModel:
    """任务模型测试"""
    
    def test_create_quest(self, test_db):
        """测试创建任务"""
        quest_id = Quest.create(
            name="测试任务",
            description="一个测试任务",
            quest_type=1,
            min_level=1,
            repeatable=False,
            reward_exp=50,
            reward_gold=20
        )
        assert quest_id is not None
        
        quest = Quest.get_by_id(quest_id)
        assert quest is not None
        assert quest[1] == "测试任务"
    
    def test_quest_progress(self, test_db):
        """测试任务进度"""
        # 创建用户、角色和任务
        user_model = User()
        user = user_model.create_user(
            phone="13800138008",
            password="test123",
            nickname="测试用户8"
        )
        character_id = Character.create(user["id"], "测试角色5")
        
        quest_id = Quest.create(
            name="测试任务2",
            description="另一个测试任务",
            quest_type=1,
            min_level=1,
            repeatable=False,
            reward_exp=50,
            reward_gold=20
        )
        
        # 开始任务
        success = QuestProgress.start_quest(character_id, quest_id)
        assert success
        
        # 完成任务
        success = QuestProgress.complete_quest(character_id, quest_id)
        assert success

class TestAchievementModel:
    """成就模型测试"""
    
    def test_create_achievement(self, test_db):
        """测试创建成就"""
        achievement_id = Achievement.create(
            name="测试成就",
            description="一个测试成就",
            achievement_type=1,
            condition_type="level_up",
            condition_value=5,
            reward_exp=100,
            reward_gold=50
        )
        assert achievement_id is not None
        
        achievement = Achievement.get_by_id(achievement_id)
        assert achievement is not None
        assert achievement[1] == "测试成就"

class TestBattleModel:
    """战斗模型测试"""
    
    def test_record_battle(self, test_db):
        """测试记录战斗"""
        # 创建用户和角色
        user_model = User()
        user = user_model.create_user(
            phone="13800138009",
            password="test123",
            nickname="测试用户9"
        )
        character_id = Character.create(user["id"], "测试角色6")
        
        # 创建敌人
        enemy_id = Enemy.create(
            name="测试敌人",
            hp=50,
            attack=10,
            defense=5,
            agility=8,
            exp_reward=20
        )
        
        # 记录战斗
        battle_id = Battle.record_battle(
            character_id=character_id,
            enemy_id=enemy_id,
            result="win",
            exp=20,
            items=["sword", "potion"]
        )
        assert battle_id is not None
        
        # 获取战斗记录
        battles = Battle.get_character_battles(character_id)
        assert len(battles) > 0
