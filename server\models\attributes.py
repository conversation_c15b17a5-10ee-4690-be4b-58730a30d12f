from server.core.db import BaseModel, db
from typing import Optional, Dict, Any
import logging

class Attributes(BaseModel):
    """角色属性模型"""
    
    def __init__(self):
        super().__init__()
        self.table_name = "attributes"
    
    @staticmethod
    def create_table():
        """创建属性表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attributes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                character_id INTEGER NOT NULL,
                strength INTEGER DEFAULT 10,
                agility INTEGER DEFAULT 10,
                intelligence INTEGER DEFAULT 10,
                vitality INTEGER DEFAULT 10,
                luck INTEGER DEFAULT 10,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (character_id) REFERENCES character(id)
            )
        ''')
        conn.commit()
    
    @staticmethod
    def create_default_attributes(character_id: int) -> int:
        """为角色创建默认属性"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO attributes (character_id, strength, agility, intelligence, vitality, luck)
            VALUES (?, 10, 10, 10, 10, 10)
        ''', (character_id,))
        conn.commit()
        return cursor.lastrowid
    
    @staticmethod
    def get_by_character_id(character_id: int) -> Optional[Dict[str, Any]]:
        """根据角色ID获取属性"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT * FROM attributes WHERE character_id = ?
        ''', (character_id,))
        result = cursor.fetchone()
        
        if not result:
            return None
        
        return {
            "id": result[0],
            "character_id": result[1],
            "strength": result[2],
            "agility": result[3],
            "intelligence": result[4],
            "vitality": result[5],
            "luck": result[6],
            "created_at": result[7],
            "updated_at": result[8]
        }
    
    @staticmethod
    def update_attribute(character_id: int, attribute_name: str, value: int) -> bool:
        """更新单个属性"""
        valid_attributes = ['strength', 'agility', 'intelligence', 'vitality', 'luck']
        if attribute_name not in valid_attributes:
            raise ValueError(f"无效的属性名: {attribute_name}")
        
        conn, cursor = db.get_db()
        query = f'''
            UPDATE attributes 
            SET {attribute_name} = ?, updated_at = CURRENT_TIMESTAMP
            WHERE character_id = ?
        '''
        cursor.execute(query, (value, character_id))
        conn.commit()
        return cursor.rowcount > 0
    
    @staticmethod
    def increase_attribute(character_id: int, attribute_name: str, amount: int = 1) -> bool:
        """增加属性值"""
        valid_attributes = ['strength', 'agility', 'intelligence', 'vitality', 'luck']
        if attribute_name not in valid_attributes:
            raise ValueError(f"无效的属性名: {attribute_name}")
        
        conn, cursor = db.get_db()
        query = f'''
            UPDATE attributes 
            SET {attribute_name} = {attribute_name} + ?, updated_at = CURRENT_TIMESTAMP
            WHERE character_id = ?
        '''
        cursor.execute(query, (amount, character_id))
        conn.commit()
        return cursor.rowcount > 0
    
    @staticmethod
    def get_total_attributes(character_id: int) -> int:
        """获取角色总属性点"""
        attributes = Attributes.get_by_character_id(character_id)
        if not attributes:
            return 0
        
        return (attributes['strength'] + attributes['agility'] + 
                attributes['intelligence'] + attributes['vitality'] + 
                attributes['luck'])
    
    @staticmethod
    def calculate_derived_stats(attributes: Dict[str, Any]) -> Dict[str, int]:
        """根据基础属性计算衍生属性"""
        return {
            "max_hp": 100 + (attributes['vitality'] - 10) * 10,
            "max_mp": 50 + (attributes['intelligence'] - 10) * 5,
            "attack_power": 10 + (attributes['strength'] - 10) * 2,
            "defense": 5 + (attributes['vitality'] - 10) * 1,
            "critical_rate": max(5, attributes['luck'] - 5),
            "dodge_rate": max(5, attributes['agility'] - 5)
        }
