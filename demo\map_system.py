import json
from typing import Dict, Any

class MapNode:
    """地图节点基类，表示三级行政区划中的任意一级"""
    
    def __init__(self, name, terrain, resources, weather, game_attributes):
        """
        初始化地图节点
        
        :param name: 节点名称 (str)
        :param terrain: 地形特征 (str)
        :param resources: 核心资源 (list[str])
        :param weather: 特殊天气 (dict)
        :param game_attributes: 游戏关联属性 (dict)
        """
        self.name = name
        self.terrain = terrain
        self.resources = resources
        self.weather = weather
        self.game_attributes = game_attributes
        self.children = []  # 子节点列表
        
    def add_child(self, child_node):
        """添加子节点"""
        self.children.append(child_node)
        
    def get_child(self, name):
        """根据名称获取子节点"""
        for child in self.children:
            if child.name == name:
                return child
        return None

class ProvinceNode(MapNode):
    """省级地图节点"""
    
    def __init__(self, name, terrain, resources, weather, game_attributes):
        super().__init__(name, terrain, resources, weather, game_attributes)
        self.type = "province"

class CityNode(MapNode):
    """地级市地图节点"""
    
    def __init__(self, name, terrain, resources, weather, game_attributes):
        super().__init__(name, terrain, resources, weather, game_attributes)
        self.type = "city"

class CountyNode(MapNode):
    """区县级地图节点"""
    
    def __init__(self, name, terrain, resources, weather, game_attributes):
        super().__init__(name, terrain, resources, weather, game_attributes)
        self.type = "county"

class MapSystem:
    """地图系统管理类"""
    
    def __init__(self):
        self.root = None  # 根节点（中国）
        self.current_node = None  # 当前所在节点
        
    def load_from_json(self, file_path: str) -> bool:
        """从JSON文件加载地图数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.root = self._build_node(data)
                self.current_node = self.root
                return True
        except Exception as e:
            print(f"加载地图数据失败: {e}")
            return False
            
    def _build_node(self, node_data: Dict[str, Any]) -> MapNode:
        """递归构建地图节点树"""
        node_type = node_data.get('type', '')
        if node_type == 'province':
            node = ProvinceNode(
                node_data['name'],
                node_data['terrain'],
                node_data['resources'],
                node_data['weather'],
                node_data['game_attributes']
            )
        elif node_type == 'city':
            node = CityNode(
                node_data['name'],
                node_data['terrain'],
                node_data['resources'],
                node_data['weather'],
                node_data['game_attributes']
            )
        elif node_type == 'county':
            node = CountyNode(
                node_data['name'],
                node_data['terrain'],
                node_data['resources'],
                node_data['weather'],
                node_data['game_attributes']
            )
        else:  # root
            node = MapNode(
                node_data['name'],
                '',
                [],
                {},
                {}
            )
            
        # 递归构建子节点
        for child_data in node_data.get('children', []):
            child_node = self._build_node(child_data)
            node.add_child(child_node)
            
        return node
        
    def move_to(self, node_name: str) -> bool:
        """移动到指定节点"""
        if not self.current_node:
            return False
            
        # 在当前节点的子节点中查找
        target = self.current_node.get_child(node_name)
        if target:
            self.current_node = target
            return True
            
        # 如果没找到，尝试在整个地图中查找
        target = self.find_node(node_name)
        if target:
            self.current_node = target
            return True
            
        return False
        
    def find_node(self, name: str) -> MapNode:
        """在整个地图中查找指定名称的节点"""
        if not self.root:
            return None
        return self._find_node_recursive(self.root, name)
        
    def _find_node_recursive(self, node: MapNode, name: str) -> MapNode:
        """递归查找节点"""
        if node.name == name:
            return node
        for child in node.children:
            found = self._find_node_recursive(child, name)
            if found:
                return found
        return None
        
    def print_current_location(self):
        """打印当前位置信息"""
        if not self.current_node:
            print("未加载地图数据")
            return
            
        print(f"\n当前位置: {self.current_node.name}")
        print(f"地形: {self.current_node.terrain}")
        print(f"资源: {', '.join(self.current_node.resources)}")
        print("天气:")
        for w_type, weathers in self.current_node.weather.items():
            print(f"  {w_type}: {', '.join(weathers)}")
        print("游戏属性:")
        for attr, value in self.current_node.game_attributes.items():
            print(f"  {attr}: {value}")

if __name__ == "__main__":
    # 测试地图系统
    map_system = MapSystem()
    if map_system.load_from_json("map_data.json"):
        print("地图数据加载成功！")
        map_system.print_current_location()
        
        # 测试移动功能
        print("\n尝试移动到'江南省'...")
        if map_system.move_to("江南省"):
            map_system.print_current_location()
            
            print("\n尝试移动到'临江城'...")
            if map_system.move_to("临江城"):
                map_system.print_current_location()
                
                print("\n尝试移动到'江湾区'...")
                if map_system.move_to("江湾区"):
                    map_system.print_current_location()