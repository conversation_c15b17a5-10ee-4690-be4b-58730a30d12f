from server.core.db import db
from datetime import datetime

class Battle:
    """战斗记录模型"""
    def __init__(self):
        self.table_name = "battles"
        self.id = None
        self.character_id = None
        self.enemy_id = None
        self.result = None  # 'win' or 'lose'
        self.experience_gained = 0
        self.items_gained = ""
        self.timestamp = datetime.now()

    @staticmethod
    def create_table():
        """创建战斗记录表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS battles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                character_id INTEGER NOT NULL,
                enemy_id INTEGER NOT NULL,
                result TEXT NOT NULL,
                experience_gained INTEGER DEFAULT 0,
                items_gained TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (character_id) REFERENCES character(id)
            )
        ''')
        conn.commit()

    @staticmethod
    def record_battle(character_id, enemy_id, result, exp, items=None):
        """记录战斗结果"""
        conn, cursor = db.get_db()
        items_str = ",".join(items) if items else ""
        cursor.execute('''
            INSERT INTO battles 
            (character_id, enemy_id, result, experience_gained, items_gained)
            VALUES (?, ?, ?, ?, ?)
        ''', (character_id, enemy_id, result, exp, items_str))
        conn.commit()
        return cursor.lastrowid

    @staticmethod
    def get_character_battles(character_id):
        """获取角色的战斗记录"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT * FROM battles 
            WHERE character_id = ?
            ORDER BY timestamp DESC
        ''', (character_id,))
        return cursor.fetchall()

    @staticmethod
    def fight(character, enemy):
        """执行战斗逻辑"""
        import random

        # 获取角色属性
        char_hp = character[5]  # hp
        char_attack = character[7]  # strength
        char_defense = character[8]  # agility (作为防御)

        # 获取敌人属性
        enemy_hp = enemy[2]  # hp
        enemy_attack = enemy[3]  # attack
        enemy_defense = enemy[4]  # defense

        battle_log = []
        round_count = 0

        while char_hp > 0 and enemy_hp > 0 and round_count < 50:  # 最多50回合
            round_count += 1

            # 角色攻击
            char_damage = max(1, char_attack + random.randint(1, 10) - enemy_defense)
            enemy_hp -= char_damage
            battle_log.append(f"第{round_count}回合：你对{enemy[1]}造成了{char_damage}点伤害")

            if enemy_hp <= 0:
                battle_log.append(f"{enemy[1]}被击败了！")
                # 记录战斗结果
                Battle.record_battle(character[0], enemy[0], "win", enemy[6])
                return True, battle_log

            # 敌人攻击
            enemy_damage = max(1, enemy_attack + random.randint(1, 5) - char_defense)
            char_hp -= enemy_damage
            battle_log.append(f"{enemy[1]}对你造成了{enemy_damage}点伤害")

            if char_hp <= 0:
                battle_log.append("你被击败了...")
                # 记录战斗结果
                Battle.record_battle(character[0], enemy[0], "lose", 0)
                return False, battle_log

        # 超时平局
        battle_log.append("战斗超时，平局")
        Battle.record_battle(character[0], enemy[0], "draw", 0)
        return False, battle_log

    @staticmethod
    def calculate_damage(attacker, defender):
        """计算伤害"""
        # 基础伤害 = 攻击力 * (1 + 力量/100)
        base_damage = attacker['attack'] * (1 + attacker['strength'] / 100)
        
        # 防御减免 = 防御力 * (1 + 敏捷/100)
        defense = defender['defense'] * (1 + defender['agility'] / 100)
        
        # 最终伤害 = 基础伤害 * (1 - 防御减免/(防御减免 + 100))
        damage = base_damage * (1 - defense / (defense + 100))
        
        return max(1, int(damage))  # 至少造成1点伤害

    @staticmethod
    def fight(character, enemy):
        """执行战斗"""
        from server.models.character import Character
        
        character_hp = character['hp']
        enemy_hp = enemy['hp']
        
        battle_log = []
        
        while character_hp > 0 and enemy_hp > 0:
            # 角色攻击
            damage = Battle.calculate_damage(character, enemy)
            enemy_hp -= damage
            battle_log.append(f"{character['name']} 对 {enemy['name']} 造成 {damage} 点伤害")
            
            if enemy_hp <= 0:
                break
                
            # 敌人反击
            damage = Battle.calculate_damage(enemy, character)
            character_hp -= damage
            battle_log.append(f"{enemy['name']} 对 {character['name']} 造成 {damage} 点伤害")
        
        # 更新角色状态
        Character.update_hp(character['id'], character_hp)
        
        # 记录战斗结果
        if character_hp > 0:
            exp_gain = enemy['exp_reward']
            Character.gain_exp(character['id'], exp_gain)
            Battle.record_battle(
                character['id'], 
                enemy['id'], 
                'win', 
                exp_gain
            )
            battle_log.append(f"{character['name']} 获胜! 获得 {exp_gain} 经验值")
            return True, battle_log
        else:
            Battle.record_battle(
                character['id'], 
                enemy['id'], 
                'lose', 
                0
            )
            battle_log.append(f"{character['name']} 战败")
            return False, battle_log