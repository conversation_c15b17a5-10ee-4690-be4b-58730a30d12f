from typing import Dict, Any
from server.models.character import Character
from server.models.item import Item

class BattleService:
    """
    战斗服务类，处理核心战斗逻辑
    """
    
    @staticmethod
    def calculate_character_attack(character: Character) -> float:
        """
        计算角色攻击力（基础+装备加成）
        :param character: 角色对象
        :return: 总攻击力
        """
        base_attack = character.base_attack
        equipment_attack = sum(
            item.attack_bonus for item in character.equipped_items
            if item and item.attack_bonus
        )
        return base_attack + equipment_attack
    
    @staticmethod
    def calculate_character_defense(character: Character) -> float:
        """
        计算角色防御力（基础+装备加成）
        :param character: 角色对象
        :return: 总防御力
        """
        base_defense = character.base_defense
        equipment_defense = sum(
            item.defense_bonus for item in character.equipped_items
            if item and item.defense_bonus
        )
        return base_defense + equipment_defense
    
    @staticmethod
    def calculate_damage(attacker_attack: float, defender_defense: float) -> float:
        """
        计算伤害值
        :param attacker_attack: 攻击方攻击力
        :param defender_defense: 防御方防御力
        :return: 造成的伤害值
        """
        # 基础伤害公式：攻击力 * (1 - 防御力/(防御力+100))
        damage = attacker_attack * (1 - (defender_defense / (defender_defense + 100)))
        return max(1, round(damage))  # 确保至少造成1点伤害
    
    @staticmethod
    def battle_round(character: Character, monster_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理一个战斗回合
        :param character: 角色对象
        :param monster_data: 怪物数据
        :return: 回合结果
        """
        char_attack = BattleService.calculate_character_attack(character)
        char_defense = BattleService.calculate_character_defense(character)
        
        # 角色攻击怪物
        damage_to_monster = BattleService.calculate_damage(
            char_attack, 
            monster_data.get("defense", 0)
        )
        
        # 怪物攻击角色
        damage_to_character = BattleService.calculate_damage(
            monster_data.get("attack", 0),
            char_defense
        )
        
        return {
            "damage_to_monster": damage_to_monster,
            "damage_to_character": damage_to_character,
            "character_hp_left": character.current_hp - damage_to_character,
            "monster_hp_left": monster_data["hp"] - damage_to_monster
        }