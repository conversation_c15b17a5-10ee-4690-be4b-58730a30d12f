from fastapi import APIRouter, Depends, HTTPException
from server.models.item import Item, Equipment
from server.models.inventory import Inventory
from server.models.character import Character
from server.core.auth import get_current_user
from typing import Dict, Any

router = APIRouter(prefix="/api/v1/items", tags=["items"])

@router.get("/")
async def get_all_items():
    """获取所有物品列表"""
    return {"code": 200, "data": Item.get_all()}

@router.get("/{item_id}")
async def get_item(item_id: int):
    """获取特定物品详情"""
    item = Item.get_by_id(item_id)
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    return {"code": 200, "data": item}

@router.get("/character/{character_id}/inventory")
async def get_character_inventory(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色背包"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:  # user_id是第二个字段
        raise HTTPException(status_code=403, detail="无权访问该角色")

    inventory = Inventory.get_character_inventory(character_id)
    return {"code": 200, "data": inventory}

@router.post("/character/{character_id}/equip/{item_id}")
async def equip_item(
    character_id: int,
    item_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """装备物品"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")

    if character[1] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")

    success = Inventory.equip_item(character_id, item_id)
    if not success:
        raise HTTPException(status_code=400, detail="无法装备该物品")

    return {"code": 200, "message": "装备成功"}

@router.get("/character/{character_id}/inventory")
async def get_character_inventory(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色背包内容"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    return Inventory.get_character_inventory(character_id)

@router.post("/character/{character_id}/inventory/{item_id}/equip")
async def equip_item(
    character_id: int,
    item_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """装备物品"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    success = Inventory.equip_item(character_id, item_id)
    if not success:
        raise HTTPException(status_code=400, detail="无法装备该物品")
    
    return {"message": "装备成功"}

@router.post("/character/{character_id}/inventory/{item_id}/use")
async def use_item(
    character_id: int,
    item_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """使用物品"""
    # TODO: 实现消耗品使用逻辑
    return {"message": "物品使用功能开发中"}