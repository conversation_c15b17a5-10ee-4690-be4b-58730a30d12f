from character_system import Character, CharacterSkill
from typing import Dict, List, Optional
import random
import datetime
import os

class BattleParticipant:
    """战斗参与者基类（玩家、宠物、怪物）"""
    
    def __init__(self, name: str, attributes: Dict[str, int], skills: List[CharacterSkill]):
        self.name = name
        self.attributes = attributes  # {hp, max_hp, attack, defense, agility}
        self.skills = skills
        self.buffs = []  # 增益效果列表
        
    def take_damage(self, damage: int) -> int:
        """承受伤害并返回实际伤害值"""
        # 闪避判定 (5%基础闪避率 + 敏捷/100)
        dodge_chance = 0.05 + self.attributes.get('agility', 0) / 100
        if random.random() < dodge_chance:
            return 0  # 闪避成功
        
        actual_damage = max(1, damage - self.attributes['defense'] // 2)
        self.attributes['hp'] = max(0, self.attributes['hp'] - actual_damage)
        return actual_damage
        
    def is_alive(self) -> bool:
        """检查是否存活"""
        return self.attributes['hp'] > 0
        
    def use_skill(self, skill_name: str) -> Optional[Dict]:
        """使用技能"""
        for skill in self.skills:
            if skill.name == skill_name and skill.cooldown == 0:
                skill.cooldown = skill.effect.get('cooldown', 3)
                return skill.effect
        return None

class BattleSystem:
    """战斗系统"""
    
    def __init__(self, player: Character):
        self.player = player
        self.enemy = None
        self.state = 'idle'  # idle, active, victory, defeat
        self.turn_count = 0
        self.battle_log = []
        self.start_time = None
        self.end_time = None
        self.battle_stats = {
            'player_damage': 0,
            'enemy_damage': 0,
            'player_crits': 0,
            'enemy_crits': 0,
            'player_dodges': 0,
            'enemy_dodges': 0,
            'max_player_damage': 0,
            'max_enemy_damage': 0
        }
        
    def start_battle(self, enemy_name: str, enemy_level: int):
        """开始战斗"""
        enemy_attributes = self._generate_enemy_attributes(enemy_name, enemy_level)
        enemy_skills = [
            CharacterSkill("爪击", "造成基础伤害", {"type": "damage", "value": 1.0}, 1)
        ]
        self.enemy = BattleParticipant(enemy_name, enemy_attributes, enemy_skills)
        self.state = 'active'
        self.turn_count = 0
        self.start_time = datetime.datetime.now()
        self.battle_log = [f"战斗开始于: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}"]
        print(f"\n战斗开始! 遭遇了 {enemy_name}!")
        
    def _generate_enemy_attributes(self, name: str, level: int) -> Dict[str, int]:
        """生成敌人属性"""
        base_hp = 50 + level * 10
        base_attack = 5 + level * 2
        return {
            'hp': base_hp,
            'max_hp': base_hp,
            'attack': base_attack,
            'defense': 3 + level,
            'agility': 5 + level
        }
        
    def player_turn(self, choice: int, skill_index: Optional[int] = None) -> str:
        """处理玩家回合"""
        if self.state != 'active':
            return "战斗未进行中"
            
        self.turn_count += 1
        
        # 玩家行动
        is_critical = random.random() < 0.1  # 10%暴击率
        damage_multiplier = 1.5 if is_critical else 1.0
        
        if choice == 1:  # 普通攻击
            damage = int(self.player.attributes.attack * damage_multiplier)
            actual_damage = self.enemy.take_damage(damage)
            
            if actual_damage == 0:
                log_msg = f"第{self.turn_count}回合: {self.enemy.name} 闪避了你的攻击!"
                self.battle_stats['enemy_dodges'] += 1
            else:
                crit_text = " (暴击!)" if is_critical else ""
                log_msg = f"第{self.turn_count}回合: [普通攻击] 你对 {self.enemy.name} 造成了 {actual_damage} 点伤害!{crit_text}"
                self.battle_stats['player_damage'] += actual_damage
                self.battle_stats['max_player_damage'] = max(self.battle_stats['max_player_damage'], actual_damage)
                if is_critical:
                    self.battle_stats['player_crits'] += 1
            
            self.battle_log.append(log_msg)
            print(log_msg)
        elif choice == 2 and skill_index is not None:  # 使用技能
            skill = self.player.skills[skill_index]
            skill_effect = self.player.use_skill(skill.name, target=self.enemy)
            if skill_effect:
                if skill_effect['type'] == 'damage':
                    damage = int(self.player.attributes.attack * skill_effect['value'] * damage_multiplier)
                    actual_damage = self.enemy.take_damage(damage)
                    crit_text = " (暴击!)" if is_critical else ""
                    log_msg = f"第{self.turn_count}回合: [技能攻击] 你使用 {skill_name} 对 {self.enemy.name} 造成了 {actual_damage} 点伤害!{crit_text}"
                    self.battle_log.append(log_msg)
                    print(log_msg)
                elif skill_effect['type'] == 'effect':
                    # 处理状态效果
                    log_msg = f"第{self.turn_count}回合: [技能效果] 你使用 {skill_name} 对 {self.enemy.name} 施加了效果!"
                    self.battle_log.append(log_msg)
                    print(log_msg)
            else:
                print("无法使用该技能")
                return "invalid"
        else:
            print("无效行动")
            return "invalid"
            
        # 检查敌人是否被击败
        if not self.enemy.is_alive():
            self._end_battle(victory=True)
            return "victory"
            
        # 敌人回合
        self.enemy_turn()
        
        # 检查玩家是否被击败
        if not self.player.attributes.hp > 0:
            self._end_battle(victory=False)
            return "defeat"
            
        return "continue"
        
    def enemy_turn(self):
        """敌人行动"""
        is_critical = random.random() < 0.1  # 10%暴击率
        damage_multiplier = 1.5 if is_critical else 1.0
        
        if random.random() < 0.7:  # 70%几率普通攻击
            damage = int(self.enemy.attributes['attack'] * damage_multiplier)
            actual_damage = self.player.attributes.take_damage(damage)
            
            if actual_damage == 0:
                log_msg = f"第{self.turn_count}回合: 你闪避了 {self.enemy.name} 的攻击!"
                self.battle_stats['player_dodges'] += 1
            else:
                crit_text = " (暴击!)" if is_critical else ""
                log_msg = f"第{self.turn_count}回合: [普通攻击] {self.enemy.name} 对你造成了 {actual_damage} 点伤害!{crit_text}"
                self.battle_stats['enemy_damage'] += actual_damage
                self.battle_stats['max_enemy_damage'] = max(self.battle_stats['max_enemy_damage'], actual_damage)
                if is_critical:
                    self.battle_stats['enemy_crits'] += 1
            
            self.battle_log.append(log_msg)
            print(log_msg)
        else:  # 30%几率使用技能
            skill = random.choice(self.enemy.skills)
            skill_effect = skill.effect
            damage = int(self.enemy.attributes['attack'] * skill_effect['value'] * damage_multiplier)
            actual_damage = self.player.attributes.take_damage(damage)
            
            if actual_damage == 0:
                log_msg = f"第{self.turn_count}回合: 你闪避了 {self.enemy.name} 的 {skill.name} 技能!"
                self.battle_stats['player_dodges'] += 1
            else:
                crit_text = " (暴击!)" if is_critical else ""
                log_msg = f"第{self.turn_count}回合: [技能攻击] {self.enemy.name} 使用 {skill.name}! 对你造成了 {actual_damage} 点伤害!{crit_text}"
                self.battle_stats['enemy_damage'] += actual_damage
                self.battle_stats['max_enemy_damage'] = max(self.battle_stats['max_enemy_damage'], actual_damage)
                if is_critical:
                    self.battle_stats['enemy_crits'] += 1
            
            self.battle_log.append(log_msg)
            print(log_msg)
        
    def _end_battle(self, victory: bool):
        """结束战斗并计算奖励"""
        self.end_time = datetime.datetime.now()
        duration = self.end_time - self.start_time
        minutes, seconds = divmod(duration.total_seconds(), 60)
        
        # 创建战斗日志目录
        log_dir = "battle_logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        if victory:
            # 基础奖励
            base_exp = self.enemy.attributes['max_hp'] // 2
            base_gold = self.enemy.attributes['attack'] * 5
            
            # 战斗表现系数
            time_factor = max(0.8, min(1.2, 1.0 - (duration.total_seconds() / 300)))  # 战斗时间影响
            crit_factor = 1.0 + (self.battle_stats['player_crits'] * 0.05)  # 暴击奖励
            dodge_factor = 1.0 + (self.battle_stats['player_dodges'] * 0.03)  # 闪避奖励
            
            # 计算最终奖励
            total_exp = int(base_exp * time_factor * crit_factor * dodge_factor)
            total_gold = int(base_gold * time_factor * crit_factor * dodge_factor)
            
            # 给予奖励
            self.player.attributes.exp += total_exp
            self.player.attributes.gold += total_gold
            
            # 战斗评价
            performance_score = (time_factor + crit_factor + dodge_factor) / 3
            if performance_score > 1.15:
                rating = "S"
            elif performance_score > 1.05:
                rating = "A"
            elif performance_score > 0.95:
                rating = "B"
            else:
                rating = "C"
            
            print(f"\n胜利! 战斗评价: {rating}")
            print(f"获得 {total_exp} 经验值 (基础: {base_exp})")
            print(f"获得 {total_gold} 金币 (基础: {base_gold})")
            
            # 检查是否升级
            if self.player.attributes.exp >= self.player.attributes.level * 100:
                self.player.attributes.level_up()
                print(f"升级了! 现在等级 {self.player.attributes.level}")
        else:
            print("\n你被击败了...")
            
        self.battle_log.append(f"战斗结束于: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.battle_log.append(f"战斗持续时间: {int(minutes)}分{int(seconds)}秒")
        self.state = 'victory' if victory else 'defeat'
        
        # 保存战斗记录到文件
        log_filename = f"{log_dir}/battle_{self.start_time.strftime('%Y%m%d_%H%M%S')}.log"
        with open(log_filename, 'w', encoding='utf-8') as f:
            f.write(f"战斗结果: {'胜利' if victory else '失败'}\n")
            f.write(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"持续时间: {int(minutes)}分{int(seconds)}秒\n")
            f.write(f"敌人名称: {self.enemy.name}\n")
            f.write("\n=== 战斗统计 ===\n")
            f.write(f"总回合数: {self.turn_count}\n")
            f.write(f"玩家总伤害: {self.battle_stats['player_damage']}\n")
            f.write(f"敌人总伤害: {self.battle_stats['enemy_damage']}\n")
            f.write(f"玩家暴击次数: {self.battle_stats['player_crits']}\n")
            f.write(f"敌人暴击次数: {self.battle_stats['enemy_crits']}\n")
            f.write(f"玩家闪避次数: {self.battle_stats['player_dodges']}\n")
            f.write(f"敌人闪避次数: {self.battle_stats['enemy_dodges']}\n")
            f.write("\n=== 战斗日志 ===\n")
            f.write("\n".join(self.battle_log))
        
    def show_battle_log(self):
        """显示战斗日志和统计信息"""
        print("\n" + "="*40)
        print(" "*10 + "=== 战斗日志 ===")
        print("="*40)
        for log in self.battle_log:
            if "战斗开始" in log or "战斗结束" in log:
                print(f"\n★ {log}")
            elif "暴击" in log:
                print(f"  ! {log}")
            elif "闪避" in log:
                print(f"  ◎ {log}")
            else:
                print(f"  - {log}")
        
        print("="*40)
        print(" "*10 + "=== 战斗统计 ===")
        print("="*40)
        print(f"战斗总回合数: {self.turn_count}")
        print(f"玩家总伤害: {self.battle_stats['player_damage']}")
        print(f"敌人总伤害: {self.battle_stats['enemy_damage']}")
        print(f"玩家最高单次伤害: {self.battle_stats['max_player_damage']}")
        print(f"敌人最高单次伤害: {self.battle_stats['max_enemy_damage']}")
        print(f"玩家暴击次数: {self.battle_stats['player_crits']}")
        print(f"敌人暴击次数: {self.battle_stats['enemy_crits']}")
        print(f"玩家闪避次数: {self.battle_stats['player_dodges']}")
        print(f"敌人闪避次数: {self.battle_stats['enemy_dodges']}")
        print("="*40)
        print("===============")

if __name__ == "__main__":
    # 测试战斗系统
    from character_system import Character, CharacterClass
    
    print("测试战斗系统:")
    player = Character("测试角色", CharacterClass.SOLDIER)
    battle = BattleSystem(player)
    
    print("\n开始模拟战斗...")
    battle.start_battle("变异鼠", 1)
    
    print("\n玩家回合 - 普通攻击")
    result = battle.player_turn("attack")
    print(f"战斗结果: {result}")
    
    print("\n玩家状态:")
    print(f"HP: {player.attributes.hp}/{player.attributes.max_hp}")