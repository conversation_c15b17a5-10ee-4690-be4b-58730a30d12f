"""
测试配置文件
"""

import pytest
import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from fastapi.testclient import TestClient
from server.main import app
from server.models import init_db
from server.core.db import db

@pytest.fixture(scope="session")
def test_db():
    """创建测试数据库"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db')
    
    # 更新数据库路径
    original_path = db._db_path
    db._db_path = Path(db_path)
    
    # 初始化测试数据库
    init_db()
    
    yield db_path
    
    # 清理
    os.close(db_fd)
    os.unlink(db_path)
    db._db_path = original_path

@pytest.fixture
def client(test_db):
    """创建测试客户端"""
    with TestClient(app) as test_client:
        yield test_client

@pytest.fixture
def test_user_data():
    """测试用户数据"""
    return {
        "phone": "13800138000",
        "password": "test123456",
        "nickname": "测试用户"
    }

@pytest.fixture
def authenticated_client(client, test_user_data):
    """已认证的测试客户端"""
    # 注册用户
    response = client.post("/api/v1/register", json=test_user_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    
    # 返回带认证头的客户端
    client.headers.update({"Authorization": f"Bearer {token}"})
    return client, token

@pytest.fixture
def test_character_data():
    """测试角色数据"""
    return {
        "name": "测试角色"
    }

@pytest.fixture
def test_character(authenticated_client, test_character_data):
    """创建测试角色"""
    client, token = authenticated_client
    
    # 创建角色
    response = client.post("/api/v1/characters/create", params=test_character_data)
    assert response.status_code == 200
    
    character_id = response.json()["data"]["character_id"]
    return character_id, client
