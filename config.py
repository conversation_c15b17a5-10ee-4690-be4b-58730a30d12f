# 游戏配置文件

DATABASE_CONFIG = {
    'engine': 'sqlite',
    'database': 'game.db',
    'pool_size': 5
}

GAME_SETTINGS = {
    'max_players': 100,
    'default_region': 'wasteland',
    'starting_level': 1,
    'max_inventory_size': 20
}

# 日志配置
LOGGING = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'filename': 'game.log'
}

# API配置
API_CONFIG = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': True
}