import json
import os
from faker import Faker
from datetime import datetime
import random

fake = Faker('zh_CN')

# 确保数据目录存在
os.makedirs('data/skills', exist_ok=True)
os.makedirs('data/monsters', exist_ok=True)
os.makedirs('data/items', exist_ok=True)
os.makedirs('data/pets', exist_ok=True)

def generate_skills(num=100):
    """生成技能数据"""
    skill_types = ['attack', 'heal', 'buff', 'debuff', 'summon']
    elements = ['fire', 'water', 'earth', 'wind', 'light', 'dark']
    
    for i in range(1, num + 1):
        skill = {
            "id": f"skill_{i:03d}",
            "name": f"{random.choice(elements)}{fake.word()}",
            "type": random.choice(skill_types),
            "damage": random.randint(10, 200),
            "cost": random.randint(5, 50),
            "cooldown": random.randint(1, 10),
            "description": fake.sentence(),
            "upgrades": []
        }
        
        # 添加升级等级
        for level in range(2, random.randint(3, 6)):
            skill["upgrades"].append({
                "level": level,
                "damage": skill["damage"] * level,
                "cost": skill["cost"] * level
            })
            
        with open(f'data/skills/{skill["id"]}.json', 'w', encoding='utf-8') as f:
            json.dump(skill, f, ensure_ascii=False, indent=2)

def generate_monsters(num=50):
    """生成怪物数据"""
    monster_types = ['zombie', 'mutant', 'robot', 'beast', 'ghost']
    
    for i in range(1, num + 1):
        monster = {
            "id": f"monster_{i:03d}",
            "name": f"{fake.word()}{random.choice(['怪', '兽', '魔', '尸'])}",
            "type": random.choice(monster_types),
            "hp": random.randint(50, 500),
            "attack": random.randint(5, 50),
            "defense": random.randint(1, 20),
            "skills": [],
            "drops": []
        }
        
        # 添加掉落物品
        for _ in range(random.randint(1, 5)):
            monster["drops"].append({
                "item": f"item_{random.randint(1, 100):03d}",
                "chance": round(random.uniform(0.1, 0.9), 2)
            })
            
        with open(f'data/monsters/{monster["id"]}.json', 'w', encoding='utf-8') as f:
            json.dump(monster, f, ensure_ascii=False, indent=2)

def generate_items(num=100):
    """生成物品数据"""
    item_types = ['weapon', 'armor', 'consumable', 'material', 'quest']
    
    for i in range(1, num + 1):
        item = {
            "id": f"item_{i:03d}",
            "name": f"{fake.word()}{random.choice(['剑', '药', '甲', '石'])}",
            "type": random.choice(item_types),
            "value": random.randint(1, 1000),
            "weight": round(random.uniform(0.1, 5.0), 1),
            "description": fake.sentence(),
            "usable": random.choice([True, False])
        }
        
        with open(f'data/items/{item["id"]}.json', 'w', encoding='utf-8') as f:
            json.dump(item, f, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    print("开始生成游戏数据...")
    generate_skills()
    generate_monsters()
    generate_items()
    print("数据生成完成！")