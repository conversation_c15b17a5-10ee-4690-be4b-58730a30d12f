from server.core.db import db

class Skill:
    """技能模型"""
    def __init__(self):
        self.table_name = "skills"
        self.id = None
        self.name = ""
        self.description = ""
        self.effect = ""
        self.mana_cost = 0
        self.cooldown = 0
        self.required_level = 1

    @staticmethod
    def create_table():
        """创建技能表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS skills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                effect TEXT,
                mana_cost INTEGER DEFAULT 0,
                cooldown INTEGER DEFAULT 0,
                required_level INTEGER DEFAULT 1
            )
        ''')
        conn.commit()

    @staticmethod
    def get_by_id(skill_id):
        """根据ID获取技能"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM skills WHERE id = ?', (skill_id,))
        return cursor.fetchone()

    @staticmethod
    def get_all():
        """获取所有技能"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM skills')
        return cursor.fetchall()

    @staticmethod
    def create(name, description="", effect="", mana_cost=0, cooldown=0, required_level=1):
        """创建新技能"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO skills
            (name, description, effect, mana_cost, cooldown, required_level)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, description, effect, mana_cost, cooldown, required_level))
        conn.commit()
        return cursor.lastrowid

    @staticmethod
    def get_all():
        """获取所有技能"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM skills')
        return cursor.fetchall()