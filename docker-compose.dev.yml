version: '3.8'

services:
  # 开发环境MUD游戏API服务
  mud-api-dev:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG_MODE=true
      - ENVIRONMENT=development
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - SECRET_KEY=development-secret-key-not-for-production
    volumes:
      - .:/app
      - ./game.db:/app/game.db
    restart: unless-stopped
    command: uvicorn server.main:app --host 0.0.0.0 --port 8000 --reload
    stdin_open: true
    tty: true
