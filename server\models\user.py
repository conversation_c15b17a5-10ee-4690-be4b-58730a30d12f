from passlib.context import Crypt<PERSON>ontext
from datetime import datetime
from typing import Optional
from contextlib import contextmanager
from ..config import DATABASE_CONFIG
from .db import db

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User:
    def __init__(self):
        self.conn, self.cursor = db.get_db()

    @staticmethod
    def hash_password(password: str) -> str:
        """哈希密码"""
        return pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)

    def create_user(self, phone: str, password: str, nickname: Optional[str] = None) -> dict:
        """创建新用户"""
        if not nickname:
            nickname = f"幸存者{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
        hashed_password = self.hash_password(password)
        
        self.cursor.execute(
            "INSERT INTO user (phone, password, nickname, current_character_id) VALUES (?, ?, ?, NULL)",
            (phone, hashed_password, nickname)
        )
        self.conn.commit()
        
        return {
            "id": self.cursor.lastrowid,
            "phone": phone,
            "nickname": nickname,
            "current_character_id": None
        }

    def get_active_character(self, user_id: int) -> Optional[dict]:
        """获取用户当前活跃角色"""
        from .character import Character
        char = Character()
        
        self.cursor.execute(
            "SELECT current_character_id FROM user WHERE id = ?",
            (user_id,)
        )
        result = self.cursor.fetchone()
        
        if not result or not result[0]:
            return None
            
        characters = char.get_characters_by_user(user_id)
        return next((c for c in characters if c['id'] == result[0]), None)

    def get_user_by_phone(self, phone: str) -> Optional[dict]:
        """通过手机号获取用户"""
        self.cursor.execute(
            "SELECT id, phone, password, nickname FROM user WHERE phone = ?", 
            (phone,)
        )
        result = self.cursor.fetchone()
        
        if not result:
            return None
            
        return {
            "id": result[0],
            "phone": result[1],
            "password": result[2],
            "nickname": result[3]
        }

    def update_current_character(self, user_id: int, character_id: int) -> None:
        """更新用户当前角色ID"""
        self.cursor.execute(
            "UPDATE user SET current_character_id = ? WHERE id = ?",
            (character_id, user_id)
        )
        self.conn.commit()

    def get_user_by_id(self, user_id: int) -> Optional[dict]:
        """通过ID获取用户"""
        self.cursor.execute(
            "SELECT id, phone, password, nickname FROM user WHERE id = ?", 
            (user_id,)
        )
        result = self.cursor.fetchone()
        
        if not result:
            return None
            
        return {
            "id": result[0],
            "phone": result[1],
            "password": result[2],
            "nickname": result[3]
        }