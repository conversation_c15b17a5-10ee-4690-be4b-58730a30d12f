《末日觉醒》MUD游戏完整设计方案（Python技术栈）
一、项目概述
1.1 核心定位
​类型​：末日异能灾难题材开放世界MUD文字游戏（H5→APP→微信小程序）
​核心玩法​：探索、战斗、经营、社交、策略
​目标用户​：末日题材爱好者、文字游戏玩家、策略经营用户
1.2 技术选型
层级	技术/工具	说明
前端	H5（Vue.js + Element UI）	前期快速开发；后期扩展APP（Flutter）和微信小程序（Taro）
后端	Python + FastAPI	高性能异步API服务；支持WebSocket长连接
数据库	MySQL 8.0 + Redis 7.0	MySQL存储结构化数据；Redis缓存热点数据（玩家状态、天气、战斗缓存）
开发阶段	SQLite 3.42	本地开发测试，数据文件便于版本控制
AI引擎	Scikit-learn + TensorFlow	动态事件生成、NPC行为决策、技能进化模型
部署	Docker + Nginx	容器化部署；Nginx反向代理+负载均衡
二、数据库设计（MySQL核心表结构）
2.1 基础数据表
user（用户表）
sql
复制
CREATE TABLE `user` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `password` varchar(64) NOT NULL COMMENT 'MD5+盐值密码',
  `nickname` varchar(32) NOT NULL DEFAULT '幸存者' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像URL',
  `register_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后登录时间',
  `game_coin` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '游戏币（废土币）',
  `energy_crystal` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '能量晶核（高级货币）',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态（0=封禁，1=正常）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_phone` (`phone`),
  UNIQUE KEY `uniq_nickname` (`nickname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
character（角色表）
sql
复制
CREATE TABLE `character` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '所属用户ID',
  `name` varchar(32) NOT NULL COMMENT '角色名',
  `gender` enum('male','female') NOT NULL DEFAULT 'male' COMMENT '性别',
  `class` varchar(32) NOT NULL COMMENT '异能职业（元素使/心灵感应者等）',
  `level` smallint UNSIGNED NOT NULL DEFAULT 1 COMMENT '等级',
  `exp` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '经验值',
  `hp` int UNSIGNED NOT NULL DEFAULT 100 COMMENT '生命值',
  `mp` int UNSIGNED NOT NULL DEFAULT 50 COMMENT '异能值',
  `attack` smallint UNSIGNED NOT NULL DEFAULT 10 COMMENT '攻击力',
  `defense` smallint UNSIGNED NOT NULL DEFAULT 5 COMMENT '防御力',
  `position_x` int NOT NULL COMMENT '地图X坐标（像素）',
  `position_y` int NOT NULL COMMENT '地图Y坐标（像素）',
  `sanctuary_id` bigint UNSIGNED DEFAULT NULL COMMENT '所属庇护区ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sanctuary` (`sanctuary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
2.2 核心业务表
world_map（世界地图表）
sql
复制
CREATE TABLE `world_map` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '地图块ID',
  `country` varchar(32) NOT NULL COMMENT '所属国家（中国/美国等）',
  `region` varchar(64) NOT NULL COMMENT '省级区域（如"四川省"）',
  `pixel_x` int NOT NULL COMMENT '像素X坐标',
  `pixel_y` int NOT NULL COMMENT '像素Y坐标',
  `terrain_type` enum('plain','mountain','desert','forest','water') NOT NULL DEFAULT 'plain' COMMENT '地形类型',
  `weather_type` varchar(32) NOT NULL DEFAULT 'sunny' COMMENT '当前天气（晴/暴雨/极寒等）',
  `resource_id` int UNSIGNED DEFAULT NULL COMMENT '关联资源点ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_pixel` (`pixel_x`,`pixel_y`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
pet（宠物表）
sql
复制
CREATE TABLE `pet` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '宠物ID',
  `owner_id` bigint UNSIGNED NOT NULL COMMENT '主人角色ID',
  `name` varchar(32) NOT NULL COMMENT '宠物名',
  `species` varchar(32) NOT NULL COMMENT '种类（战斗/采集/辅助等）',
  `level` smallint UNSIGNED NOT NULL DEFAULT 1 COMMENT '等级',
  `hp` int UNSIGNED NOT NULL DEFAULT 50 COMMENT '生命值',
  `attack` smallint UNSIGNED NOT NULL DEFAULT 5 COMMENT '攻击力',
  `loyalty` tinyint UNSIGNED NOT NULL DEFAULT 50 COMMENT '忠诚度（0-100）',
  `skill_id` int UNSIGNED DEFAULT NULL COMMENT '技能ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
sanctuary（庇护区表）
sql
复制
CREATE TABLE `sanctuary` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '庇护区ID',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `leader_id` bigint UNSIGNED NOT NULL COMMENT '领袖角色ID',
  `level` smallint UNSIGNED NOT NULL DEFAULT 1 COMMENT '等级',
  `defense` int UNSIGNED NOT NULL DEFAULT 100 COMMENT '防御值',
  `resource_income` int UNSIGNED NOT NULL DEFAULT 100 COMMENT '每小时资源产出',
  `member_count` smallint UNSIGNED NOT NULL DEFAULT 1 COMMENT '成员数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_leader` (`leader_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
2.3 动态数据表（Redis键设计）
键前缀	数据结构	说明
user:{uid}	Hash	用户实时状态（游戏币、能量晶核、在线状态）
char:{cid}	Hash	角色实时状态（HP/MP/位置/装备）
weather:{rid}	String	区域天气状态（JSON格式：{"type":"rain","effect":"move-15%"}）
battle:{bid}	Hash	战斗缓存（参战角色/宠物、当前回合、技能冷却）
event:{eid}	JSON	动态事件状态（触发条件、进度、奖励）
三、核心系统实现
3.1 世界地图与地形系统
3.1.1 地图生成与存储
​数据来源​：基于真实世界地图投影（墨卡托投影），1像素=1平方公里
​生成逻辑​：
python
运行
复制
# 示例：中国区域地图生成（简化版）
def generate_china_map():
    countries = ["中国"]
    regions = ["北京市", "上海市", "重庆市", "四川省"]  # 省级区域
    terrain_types = ["plain", "mountain", "desert", "forest", "water"]
    
    for country in countries:
        for region in regions:
            # 根据真实地理数据分配地形比例（示例）
            terrain_dist = {"plain": 0.4, "mountain": 0.3, "desert": 0.1, "forest": 0.15, "water": 0.05}
            for _ in range(1000):  # 生成1000个地图块
                pixel_x = random.randint(10000, 12000)  # 假设中国区域像素范围
                pixel_y = random.randint(20000, 40000)
                terrain = random.choices(list(terrain_dist.keys()), weights=terrain_dist.values())[0]
                world_map.insert({
                    "country": country,
                    "region": region,
                    "pixel_x": pixel_x,
                    "pixel_y": pixel_y,
                    "terrain_type": terrain,
                    "weather_type": "sunny"
                })
​动态修改​：通过UPDATE world_map SET terrain_type=? WHERE pixel_x=? AND pixel_y=?更新地形（如地震后变为废墟）
3.1.2 天气系统
​天气类型与效果​：

天气	效果描述
晴	无特殊效果
暴雨	移动速度-15%，火系技能伤害-30%
极寒	每秒损失0.5% HP，冰系技能伤害+20%
雷暴	随机触发落雷（对单个角色造成100%魔法伤害）
辐射雾	变异怪物刷新概率+50%，变异器官掉落概率+30%
​天气同步​：

定时任务（每30分钟）：UPDATE world_map SET weather_type=? WHERE region=?
实时推送：通过WebSocket向区域内玩家发送WEATHER_UPDATE事件
3.2 角色成长系统
3.2.1 升级与经验
​经验公式​：基础经验 = 怪物等级² × 10 × (1 - |玩家等级-怪物等级|/50)
​升级逻辑​：
python
运行
复制
def gain_exp(character_id, exp):
    char = db.query("SELECT * FROM character WHERE id=?", character_id)
    current_level = char["level"]
    exp_needed = current_level ** 2 * 100  # 简化版升级所需经验
    
    if char["exp"] + exp >= exp_needed:
        new_level = current_level + 1
        # 属性成长（非线性）
        new_hp = char["hp"] + (new_level * 10)
        new_attack = char["attack"] + (new_level * 2)
        new_defense = char["defense"] + (new_level * 1)
        
        db.execute("""
            UPDATE character 
            SET level=?, exp=?, hp=?, attack=?, defense=? 
            WHERE id=?
        """, new_level, 0, new_hp, new_attack, new_defense, character_id)
        return new_level
    else:
        db.execute("UPDATE character SET exp=exp+? WHERE id=?", exp, character_id)
        return current_level
3.2.2 技能卡牌系统
​技能表（skill_card）​​：

sql
复制
CREATE TABLE `skill_card` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `name` varchar(64) NOT NULL COMMENT '技能名（如"火球术"）',
  `type` enum('attack','defense','support') NOT NULL COMMENT '类型',
  `cost` smallint UNSIGNED NOT NULL COMMENT 'MP消耗',
  `cooldown` tinyint UNSIGNED NOT NULL COMMENT '冷却时间（回合）',
  `effect` text NOT NULL COMMENT '效果描述（JSON格式）',
  `rarity` enum('C','B','A','S') NOT NULL DEFAULT 'C' COMMENT '稀有度',
  `source` varchar(32) NOT NULL COMMENT '获取途径（boss/quest/craft）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
​技能装备逻辑​：

python
运行
复制
# 角色装备技能（最多6个）
def equip_skill(character_id, skill_id):
    char = db.query("SELECT * FROM character WHERE id=?", character_id)
    equipped_skills = json.loads(char["equipped_skills"] or "[]")
    
    if len(equipped_skills) >= 6:
        return {"code": 400, "msg": "技能槽已满"}
    
    # 检查技能是否存在且未装备
    skill = db.query("SELECT * FROM skill_card WHERE id=?", skill_id)
    if not skill or skill_id in equipped_skills:
        return {"code": 404, "msg": "技能不存在或已装备"}
    
    equipped_skills.append(skill_id)
    db.execute("UPDATE character SET equipped_skills=? WHERE id=?", 
              json.dumps(equipped_skills), character_id)
    return {"code": 200, "msg": "技能装备成功"}
3.3 宠物系统
3.3.1 宠物养成
​成长逻辑​：
python
运行
复制
def feed_pet(pet_id, food_id):
    pet = db.query("SELECT * FROM pet WHERE id=?", pet_id)
    food = db.query("SELECT * FROM item WHERE id=?", food_id)
    
    # 提升忠诚度和经验
    new_loyalty = min(pet["loyalty"] + food["loyalty_bonus"], 100)
    new_exp = pet["exp"] + food["exp_bonus"]
    
    # 检查是否升级
    exp_needed = pet["level"] ** 2 * 50
    if new_exp >= exp_needed:
        new_level = pet["level"] + 1
        new_hp = pet["hp"] + (new_level * 5)
        new_attack = pet["attack"] + (new_level * 1)
        
        db.execute("""
            UPDATE pet 
            SET level=?, hp=?, attack=?, loyalty=?, exp=? 
            WHERE id=?
        """, new_level, new_hp, new_attack, new_loyalty, 0, pet_id)
    else:
        db.execute("""
            UPDATE pet 
            SET loyalty=?, exp=? 
            WHERE id=?
        """, new_loyalty, new_exp, pet_id)
    
    return {"level": pet["level"] + (1 if new_exp >= exp_needed else 0)}
3.3.2 宠物战斗
​战斗参与逻辑​：
python
运行
复制
# 战斗回合处理（简化版）
def battle_turn(battle_id, pet_id, skill_id):
    battle = db.query("SELECT * FROM battle WHERE id=?", battle_id)
    pet = db.query("SELECT * FROM pet WHERE id=?", pet_id)
    skill = db.query("SELECT * FROM skill_card WHERE id=?", skill_id)
    
    # 计算伤害
    damage = int(pet["attack"] * skill["damage_multiplier"] * (1 - battle["enemy_defense"]/100))
    battle["enemy_hp"] -= damage
    
    # 更新战斗状态
    db.execute("UPDATE battle SET enemy_hp=? WHERE id=?", battle["enemy_hp"], battle_id)
    return {"damage": damage, "enemy_hp": battle["enemy_hp"]}
3.4 庇护区（战盟）系统
3.4.1 要塞升级
​升级配置表（sanctuary_upgrade）​​：

sql
复制
CREATE TABLE `sanctuary_upgrade` (
  `level` smallint UNSIGNED NOT NULL COMMENT '目标等级',
  `defense` int UNSIGNED NOT NULL COMMENT '防御值提升',
  `resource_income` int UNSIGNED NOT NULL COMMENT '资源产出提升',
  `cost_steel` int UNSIGNED NOT NULL COMMENT '需要钢材',
  `cost_concrete` int UNSIGNED NOT NULL COMMENT '需要混凝土',
  `build_time` int UNSIGNED NOT NULL COMMENT '建造时间（秒）',
  PRIMARY KEY (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
​升级逻辑​：

python
运行
复制
def upgrade_sanctuary(sanctuary_id, user_id):
    # 检查权限（领袖或指挥官）
    leader = db.query("SELECT leader_id FROM sanctuary WHERE id=?", sanctuary_id)
    if leader["leader_id"] != user_id:
        return {"code": 403, "msg": "无权限"}
    
    # 检查资源
    user = db.query("SELECT steel, concrete FROM user WHERE id=?", user_id)
    upgrade_config = db.query("SELECT * FROM sanctuary_upgrade WHERE level=?", 
                             sanctuary["level"] + 1)
    
    if user["steel"] < upgrade_config["cost_steel"] or user["concrete"] < upgrade_config["cost_concrete"]:
        return {"code": 400, "msg": "资源不足"}
    
    # 扣除资源并开始建造
    db.execute("""
        UPDATE user SET steel=steel-?, concrete=concrete-? WHERE id=?
    """, upgrade_config["cost_steel"], upgrade_config["cost_concrete"], user_id)
    
    db.execute("""
        UPDATE sanctuary SET 
            level=level+1,
            defense=defense+?,
            resource_income=resource_income+?,
            build_progress=? 
        WHERE id=?
    """, upgrade_config["defense"], upgrade_config["resource_income"], 
       upgrade_config["build_time"], sanctuary_id)
    
    # 异步任务：建造完成后更新状态
    async_task("complete_sanctuary_upgrade", sanctuary_id)
    return {"code": 200, "msg": "升级开始"}
3.4.2 资源争夺战
​战争流程​：

​宣战​：庇护区领袖消耗strategic_material发起宣战（记录到war_declaration表）
​备战​：双方布置防御工事、雇佣NPC
​交战​：按三路推进，核心区域攻防战（实时同步战斗状态）
​结算​：根据摧毁建筑数量、击杀数分配奖励
​战争奖励表（war_reward）​​：

sql
复制
CREATE TABLE `war_reward` (
  `war_id` bigint UNSIGNED NOT NULL COMMENT '战争ID',
  `sanctuary_id` bigint UNSIGNED NOT NULL COMMENT '胜利庇护区ID',
  `resource` int UNSIGNED NOT NULL COMMENT '获得的资源量',
  `prestige` int UNSIGNED NOT NULL COMMENT '声望值',
  PRIMARY KEY (`war_id`, `sanctuary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
3.5 AI演进系统
3.5.1 NPC行为决策
​行为树示例（JSON）​​：
json
复制
{
  "root": "survival",
  "nodes": {
    "survival": {
      "type": "priority",
      "children": ["avoid_danger", "find_food", "collect_items"]
    },
    "avoid_danger": {
      "type": "condition",
      "check": "threat_level > 0.7",
      "action": "flee"
    },
    "find_food": {
      "type": "sequence",
      "children": ["detect_food", "move_to", "consume"]
    },
    "collect_items": {
      "type": "selector",
      "children": ["find_weapons", "find_medicine"]
    }
  }
}
3.5.2 动态事件生成
​事件生成模型​：
python
运行
复制
def generate_event(region):
    # 获取区域状态
    region_data = db.query("SELECT * FROM world_map WHERE region=?", region)
    player_count = db.query("SELECT COUNT(*) FROM character WHERE position_x=? AND position_y=?", 
                           region_data["pixel_x"], region_data["pixel_y"])
    weather = region_data["weather_type"]
    
    # 基于规则的触发
    if player_count > 50 and weather == "clear":
        return "werewolf_invasion"  # 狼人入侵事件
    
    if weather == "acid_rain" and region_data["terrain_type"] == "forest":
        return "mutant_plant"  # 变异植物事件
    
    # 机器学习预测（示例）
    event_prob = ml_model.predict([[player_count, weather_score]])
    if random.random() < event_prob:
        return random.choice(["bandit_raid", "resource_boom"])
    return None
四、前后端协作设计
4.1 API接口规范（FastAPI）
4.1.1 用户登录（H5）
python
运行
复制
# FastAPI路由示例
from fastapi import FastAPI, Depends, HTTPException
from pydantic import BaseModel

app = FastAPI()

class LoginRequest(BaseModel):
    phone: str
    password: str

@app.post("/api/login")
async def login(req: LoginRequest):
    user = db.query("SELECT * FROM user WHERE phone=? AND password=?", req.phone, req.password)
    if not user:
        raise HTTPException(status_code=401, detail="账号或密码错误")
    
    # 生成JWT Token
    token = jwt.encode({"uid": user["id"], "exp": datetime.now() + timedelta(days=7)}, SECRET_KEY)
    
    # 更新最后登录时间
    db.execute("UPDATE user SET last_login_time=CURRENT_TIMESTAMP WHERE id=?", user["id"])
    
    return {"code": 200, "token": token, "user": {"nickname": user["nickname"], "level": user.get("level", 1)}}
4.1.2 角色移动（WebSocket）
javascript
运行
复制
// H5客户端WebSocket通信示例
const ws = new WebSocket("wss://api.example.com/ws");
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: "MOVE",
    direction: "north",
    speed: 5
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === "POSITION_UPDATE") {
    // 更新角色位置显示
    document.getElementById("position").textContent = `X:${data.x}, Y:${data.y}`;
  }
};
五、部署方案
5.1 开发环境（本地）
​数据库​：SQLite（game.db）
​后端​：Python 3.10 + FastAPI + Uvicorn
​前端​：Vue.js 3 + Vite（H5）
​工具​：Docker Compose（本地容器化）
5.2 生产环境（服务器）
​架构​：Docker Swarm集群
​服务拆分​：
api-gateway：Nginx反向代理+负载均衡
world-service：世界地图、地形服务
character-service：角色、宠物服务
sanctuary-service：庇护区、战争服务
ai-service：AI演进、事件生成
db-proxy：MySQL读写分离代理
redis-cache：Redis哨兵模式（主从复制）
5.3 监控与日志
​监控​：Prometheus + Grafana（CPU/内存/请求量）
​日志​：ELK Stack（Elasticsearch + Logstash + Kibana）
​告警​：Alertmanager（内存使用率>80%、数据库连接数>上限）
六、开发计划
6.1 第一阶段（1个月）：核心框架
完成FastAPI基础API（用户登录、角色创建）
实现SQLite→MySQL数据迁移脚本
开发H5基础UI（登录/注册/角色列表）
6.2 第二阶段（2个月）：核心玩法
实现世界地图探索（地形/天气）
完成战斗系统（技能/宠物）
上线首充活动（充值送装备）
6.3 第三阶段（1个月）：社交与经济
庇护区系统（创建/升级/成员管理）
拍卖行（玩家交易/跨服交易）
首个赛季（主题：辐射觉醒）
6.4 第四阶段（持续迭代）：扩展内容
AI演进（动态事件/智能NPC）
跨服PVP（领地争夺/战争系统）
新资料片（机械叛乱/异能失控）
七、风险与应对
7.1 性能风险
​问题​：高并发下数据库压力大
​应对​：Redis缓存热点数据；MySQL读写分离；分表（按区域分表）
7.2 外挂风险
​问题​：玩家利用脚本刷资源
​应对​：行为检测（异常移动/战斗频率）；关键操作验证码；实时封禁系统
7.3 数据丢失风险
​问题​：数据库故障导致数据丢失
​应对​：每日全量备份（OSS存储）；每小时增量备份（Kafka日志）；灾难恢复演练


一、好友系统核心功能
1.1 基础功能
功能模块	描述
​好友申请​	发送/接受/拒绝好友请求（支持备注）
​好友列表​	查看在线/离线好友、最近互动记录
​好友聊天​	文字/表情聊天（支持历史记录查询）
​好友组队​	邀请好友组队探索/战斗（共享经验/资源）
​好友互助​	赠送资源（废土币/能量晶核）、复活好友（消耗资源）
​黑名单​	屏蔽骚扰用户（无法发送请求/消息）
1.2 扩展功能（后期迭代）
​好友标签​：自定义分组（如“核心队友”“交易伙伴”）
​好友成就​：共同完成任务获得奖励（如“好友共战10次”）
​跨服好友​：保护期结束后可添加其他区服好友
二、好友系统数据结构（MySQL）
2.1 好友关系表（friend_relation）
sql
复制
CREATE TABLE `friend_relation` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '主动方用户ID',
  `friend_id` bigint UNSIGNED NOT NULL COMMENT '好友用户ID',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态（0=申请中，1=已通过，2=已拒绝）',
  `remark` varchar(64) DEFAULT '' COMMENT '备注（如"战友-炎霜"）',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_relation` (`user_id`, `friend_id`),
  KEY `idx_friend` (`friend_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
2.2 好友聊天记录表（friend_chat）
sql
复制
CREATE TABLE `friend_chat` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `sender_id` bigint UNSIGNED NOT NULL COMMENT '发送者用户ID',
  `receiver_id` bigint UNSIGNED NOT NULL COMMENT '接收者用户ID',
  `content` text NOT NULL COMMENT '消息内容（文本/表情JSON）',
  `send_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  `is_read` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已读（0=未读，1=已读）',
  PRIMARY KEY (`id`),
  KEY `idx_sender_receiver` (`sender_id`, `receiver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
2.3 好友互助记录表（friend_help）
sql
复制
CREATE TABLE `friend_help` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `helper_id` bigint UNSIGNED NOT NULL COMMENT '帮助者用户ID',
  `receiver_id` bigint UNSIGNED NOT NULL COMMENT '被帮助者用户ID`,
  `type` enum('resource','revive') NOT NULL COMMENT '类型（资源赠送/复活）',
  `amount` int UNSIGNED DEFAULT NULL COMMENT '赠送资源量（仅resource类型有效）',
  `revive_time` datetime DEFAULT NULL COMMENT '复活时间（仅revive类型有效）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `idx_helper_receiver` (`helper_id`, `receiver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
三、好友系统接口设计（FastAPI）
3.1 好友申请接口
python
运行
复制
# 发送好友请求
@app.post("/api/friend/request")
async def send_friend_request(req: dict):
    user_id = get_current_user_id()  # 从JWT获取当前用户ID
    friend_id = req["friend_id"]
    remark = req.get("remark", "")
    
    # 校验是否已存在关系
    existing = db.query("""
        SELECT status FROM friend_relation 
        WHERE user_id=? AND friend_id=?
    """, user_id, friend_id)
    
    if existing:
        if existing["status"] == 1:
            raise HTTPException(400, "已是好友")
        elif existing["status"] == 2:
            raise HTTPException(400, "已被拒绝")
    
    # 插入申请记录（状态0=申请中）
    db.execute("""
        INSERT INTO friend_relation (user_id, friend_id, remark)
        VALUES (?, ?, ?)
    """, user_id, friend_id, remark)
    
    # 推送通知（通过WebSocket）
    notify_friend_application(friend_id, user_id, remark)
    
    return {"code": 200, "msg": "好友请求已发送"}
3.2 好友列表查询接口
python
运行
复制
# 获取好友列表（含状态和在线状态）
@app.get("/api/friend/list")
async def get_friend_list():
    user_id = get_current_user_id()
    
    # 查询已通过的好友
    friends = db.query("""
        SELECT fr.friend_id, u.nickname, u.avatar, fr.remark, 
               CASE WHEN online_users.user_id IS NOT NULL THEN 1 ELSE 0 END AS is_online
        FROM friend_relation fr
        JOIN user u ON fr.friend_id = u.id
        LEFT JOIN (
            SELECT user_id FROM user_status WHERE status=1  -- 在线状态表（需额外维护）
        ) online_users ON fr.friend_id = online_users.user_id
        WHERE fr.user_id = ? AND fr.status = 1
    """, user_id)
    
    return {"code": 200, "friends": friends}
3.3 聊天消息发送接口
python
运行
复制
# 发送好友消息
@app.post("/api/friend/chat")
async def send_chat_message(req: dict):
    sender_id = get_current_user_id()
    receiver_id = req["receiver_id"]
    content = req["content"]  # 支持文本或表情JSON（如{"type":"emoji","id":123}）
    
    # 校验是否为好友（状态1）
    relation = db.query("""
        SELECT 1 FROM friend_relation 
        WHERE (user_id=? AND friend_id=?) OR (user_id=? AND friend_id=?)
        AND status=1
    """, sender_id, receiver_id, receiver_id, sender_id)
    
    if not relation:
        raise HTTPException(403, "非好友关系无法聊天")
    
    # 插入聊天记录
    message_id = db.execute("""
        INSERT INTO friend_chat (sender_id, receiver_id, content)
        VALUES (?, ?, ?)
    """, sender_id, receiver_id, json.dumps(content))
    
    # 推送消息到接收者（WebSocket）
    push_chat_message(receiver_id, {
        "message_id": message_id,
        "sender_id": sender_id,
        "content": content,
        "send_time": datetime.now().isoformat()
    })
    
    return {"code": 200, "msg": "消息已发送", "message_id": message_id}
四、好友系统与现有系统集成
4.1 与角色系统集成
​好友组队​：组队时检查双方是否为好友（状态1），组队成功后共享经验（character.exp += 共享值）
​好友复活​：被复活好友需满足friend_help.revive_time在保护期内，复活后恢复10% HP（character.hp = MAX_HP * 0.1）
4.2 与经济系统集成
​资源赠送​：赠送资源时扣除发送者game_coin，接收者增加对应数量（需校验发送者余额）
​交易记录​：在user_transaction表中记录（类型=“好友赠送”）
4.3 与AI系统集成
​好友推荐​：AI根据玩家行为（如常一起战斗的好友）推荐潜在好友（recommended_friends表）
​聊天内容审核​：使用NLP模型过滤敏感词（集成到send_chat_message接口）
五、H5前端实现（Vue.js）
5.1 好友列表组件
vue
复制
<!-- FriendList.vue -->
<template>
  <div class="friend-list">
    <h3>好友列表</h3>
    <div v-for="friend in friends" :key="friend.friend_id" class="friend-item">
      <img :src="friend.avatar" class="avatar" />
      <div class="info">
        <span class="nickname">{{ friend.nickname }}</span>
        <span class="remark" v-if="friend.remark">({{ friend.remark }})</span>
        <span class="status" :class="{ online: friend.is_online }">
          {{ friend.is_online ? '在线' : '离线' }}
        </span>
      </div>
      <div class="actions">
        <button @click="chatWithFriend(friend.friend_id)">聊天</button>
        <button @click="deleteFriend(friend.friend_id)">删除</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      friends: []
    }
  },
  mounted() {
    this.fetchFriendList()
  },
  methods: {
    async fetchFriendList() {
      const res = await this.$http.get('/api/friend/list')
      this.friends = res.data.friends
    },
    chatWithFriend(friendId) {
      this.$router.push(`/chat/${friendId}`)
    },
    async deleteFriend(friendId) {
      await this.$http.delete(`/api/friend/${friendId}`)
      this.fetchFriendList()  // 刷新列表
    }
  }
}
</script>
5.2 聊天窗口组件
vue
复制
<!-- ChatWindow.vue -->
<template>
  <div class="chat-window">
    <div class="chat-header">
      <span>{{ friend.nickname }}</span>
    </div>
    <div class="chat-messages" ref="messagesContainer">
      <div v-for="msg in messages" :key="msg.message_id" 
           :class="['message', msg.sender_id === currentUser.id ? 'sent' : 'received']">
        <span class="time">{{ formatTime(msg.send_time) }}</span>
        <div class="content" v-html="renderContent(msg.content)"></div>
      </div>
    </div>
    <div class="chat-input">
      <textarea v-model="newMessage" placeholder="输入消息..."></textarea>
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script>
export default {
  props: ['friendId'],
  data() {
    return {
      messages: [],
      newMessage: '',
      currentUser: null,
      friend: null
    }
  },
  mounted() {
    this.currentUser = this.$store.state.user
    this.fetchFriendInfo()
    this.fetchChatMessages()
    this.setupWebSocket()  // 监听新消息
  },
  methods: {
    async fetchFriendInfo() {
      const res = await this.$http.get(`/api/user/${this.friendId}`)
      this.friend = res.data
    },
    async fetchChatMessages() {
      const res = await this.$http.get(`/api/friend/chat/history?friend_id=${this.friendId}`)
      this.messages = res.data.messages
      this.scrollToBottom()
    },
    async sendMessage() {
      if (!this.newMessage.trim()) return
      await this.$http.post('/api/friend/chat', {
        receiver_id: this.friendId,
        content: this.newMessage
      })
      this.newMessage = ''
    },
    setupWebSocket() {
      // 监听WebSocket消息（简化版）
      window.addEventListener('message', (event) => {
        const msg = JSON.parse(event.data)
        if (msg.type === 'CHAT' && msg.receiver_id === this.friendId) {
          this.messages.push(msg)
          this.scrollToBottom()
        }
      })
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
      })
    },
    renderContent(content) {
      // 渲染表情/文本（简化版）
      return content.replace(/$$emoji:(\d+)$$/g, '<img src="/emojis/$1.png" />')
    },
    formatTime(timeStr) {
      return new Date(timeStr).toLocaleTimeString()
    }
  }
}
</script>
六、部署与测试
6.1 数据库初始化
sql
复制
-- 好友系统初始化SQL（开发阶段SQLite）
CREATE TABLE IF NOT EXISTS friend_relation (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  friend_id INTEGER NOT NULL,
  status INTEGER DEFAULT 0,
  remark TEXT,
  add_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, friend_id)
);

CREATE TABLE IF NOT EXISTS friend_chat (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sender_id INTEGER NOT NULL,
  receiver_id INTEGER NOT NULL,
  content TEXT NOT NULL,
  send_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_read INTEGER DEFAULT 0
);
6.2 测试用例
测试场景	步骤	预期结果
发送好友请求	用户A发送请求给用户B	用户B收到通知，friend_relation表新增记录（状态0）
接受好友请求	用户B接受用户A的请求	friend_relation表状态变更为1，双方好友列表显示对方
发送聊天消息	用户A向用户B发送文本消息	friend_chat表新增记录，用户B的聊天窗口显示消息
删除好友	用户A删除用户B	friend_relation表记录删除，用户B的好友列表移除用户A
资源赠送	用户A向用户B赠送100废土币	用户A的game_coin减少100，用户B的game_coin增加100，交易记录新增