#!/usr/bin/env python3
"""
游戏数据初始化脚本
初始化基础游戏数据：怪物、物品、技能、任务、成就等
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from server.models import init_db
from server.models.monster import Monster
from server.models.enemy import Enemy
from server.models.item import Item, Equipment, ItemType, EquipmentSlot
from server.models.skill import Skill
from server.models.quest import Quest
from server.models.achievement import Achievement

def init_monsters():
    """初始化怪物数据"""
    print("初始化怪物数据...")
    
    monsters = [
        ("小史莱姆", 1, 50, 8, 2, 10, 0.1, []),
        ("哥布林", 2, 80, 12, 4, 20, 0.15, [1, 2]),
        ("骷髅兵", 3, 120, 18, 6, 35, 0.2, [2, 3]),
        ("巨魔", 5, 200, 25, 10, 60, 0.25, [3, 4]),
        ("暗影狼", 4, 150, 22, 8, 45, 0.2, [2, 5]),
        ("火焰蜘蛛", 6, 180, 30, 12, 80, 0.3, [4, 6]),
        ("冰霜巨人", 8, 350, 40, 20, 150, 0.35, [5, 7]),
        ("末日使者", 10, 500, 60, 30, 300, 0.5, [6, 7, 8])
    ]
    
    for monster_data in monsters:
        Monster.create(*monster_data)
    
    print(f"已创建 {len(monsters)} 个怪物")

def init_enemies():
    """初始化敌人数据"""
    print("初始化敌人数据...")
    
    enemies = [
        ("变异老鼠", 30, 6, 1, 8, 5, "辐射变异的老鼠，速度很快"),
        ("废土强盗", 60, 15, 5, 6, 15, "末日后的人类强盗"),
        ("机械守卫", 100, 20, 15, 3, 25, "战前遗留的机械守卫"),
        ("辐射蟑螂", 40, 10, 3, 10, 8, "巨大的变异蟑螂"),
        ("死亡爪兽", 150, 35, 8, 12, 50, "危险的变异生物")
    ]
    
    for enemy_data in enemies:
        Enemy.create(*enemy_data)
    
    print(f"已创建 {len(enemies)} 个敌人")

def init_items():
    """初始化物品数据"""
    print("初始化物品数据...")
    
    # 基础物品
    items = [
        ("生命药水", ItemType.CONSUMABLE, "恢复50点生命值", 10),
        ("魔法药水", ItemType.CONSUMABLE, "恢复30点魔法值", 15),
        ("面包", ItemType.CONSUMABLE, "恢复少量生命值", 5),
        ("铁矿石", ItemType.MATERIAL, "制作装备的基础材料", 20),
        ("皮革", ItemType.MATERIAL, "制作防具的材料", 15),
        ("神秘水晶", ItemType.MATERIAL, "稀有的魔法材料", 100),
        ("任务卷轴", ItemType.QUEST, "重要的任务物品", 0),
        ("钥匙", ItemType.QUEST, "打开特殊门锁的钥匙", 0)
    ]
    
    for item_data in items:
        Item.create(*item_data)
    
    # 装备物品
    equipment_items = [
        ("新手剑", ItemType.EQUIPMENT, "基础的铁剑", 50),
        ("皮甲", ItemType.EQUIPMENT, "基础的皮制护甲", 40),
        ("铁盾", ItemType.EQUIPMENT, "坚固的铁制盾牌", 60),
        ("法师帽", ItemType.EQUIPMENT, "增加魔法力的帽子", 80),
        ("战士靴", ItemType.EQUIPMENT, "增加移动速度的靴子", 45),
        ("魔法戒指", ItemType.EQUIPMENT, "神秘的魔法戒指", 120),
        ("钢剑", ItemType.EQUIPMENT, "锋利的钢制长剑", 150),
        ("龙鳞甲", ItemType.EQUIPMENT, "传说中的龙鳞护甲", 500)
    ]
    
    equipment_stats = [
        (EquipmentSlot.WEAPON, 15, 0, 0, 100),      # 新手剑
        (EquipmentSlot.CHEST, 0, 8, 20, 100),       # 皮甲
        (EquipmentSlot.WEAPON, 5, 12, 0, 100),      # 铁盾
        (EquipmentSlot.HEAD, 0, 3, 15, 100),        # 法师帽
        (EquipmentSlot.FEET, 2, 5, 10, 100),        # 战士靴
        (EquipmentSlot.ACCESSORY, 8, 2, 25, 100),   # 魔法戒指
        (EquipmentSlot.WEAPON, 25, 0, 0, 100),      # 钢剑
        (EquipmentSlot.CHEST, 0, 20, 50, 100)       # 龙鳞甲
    ]
    
    for i, item_data in enumerate(equipment_items):
        item_id = Item.create(*item_data)
        Equipment.create_equipment(item_id, *equipment_stats[i])
    
    print(f"已创建 {len(items) + len(equipment_items)} 个物品")

def init_skills():
    """初始化技能数据"""
    print("初始化技能数据...")
    
    skills = [
        ("重击", "用力挥击，造成额外伤害", "增加30%攻击力", 10, 3, 1),
        ("治疗术", "恢复生命值", "恢复50点生命值", 15, 0, 2),
        ("火球术", "发射火球攻击敌人", "造成魔法伤害", 20, 5, 3),
        ("防御姿态", "提高防御力", "减少50%受到的伤害", 12, 8, 2),
        ("疾风步", "快速移动", "增加闪避率", 8, 4, 1),
        ("冰霜箭", "发射冰霜箭", "造成冰霜伤害并减速", 25, 6, 4),
        ("狂暴", "进入狂暴状态", "大幅提升攻击力但降低防御", 30, 15, 5),
        ("群体治疗", "治疗多个目标", "恢复所有队友的生命值", 40, 0, 6),
        ("雷电术", "召唤雷电攻击", "造成大量魔法伤害", 35, 8, 7),
        ("终极斩击", "最强的物理攻击", "造成巨额物理伤害", 50, 20, 10)
    ]
    
    for skill_data in skills:
        Skill.create(*skill_data)
    
    print(f"已创建 {len(skills)} 个技能")

def init_quests():
    """初始化任务数据"""
    print("初始化任务数据...")
    
    quests = [
        ("新手训练", "击败3只小史莱姆", 1, 1, False, 50, 20, 1, 1),
        ("收集材料", "收集5个铁矿石", 2, 1, True, 30, 15, 4, 5),
        ("探索废墟", "探索古老的废墟", 1, 3, False, 100, 50, 2, 1),
        ("击败强敌", "击败1只巨魔", 1, 5, False, 200, 100, 7, 1),
        ("学习技能", "学会火球术", 2, 3, False, 80, 30, None, 0),
        ("装备收集", "获得一件稀有装备", 2, 4, False, 150, 75, 6, 1),
        ("等级提升", "达到10级", 1, 1, False, 300, 200, 3, 1),
        ("成就大师", "完成10个成就", 2, 8, False, 500, 300, 8, 1)
    ]
    
    for quest_data in quests:
        Quest.create(*quest_data)
    
    print(f"已创建 {len(quests)} 个任务")

def init_achievements():
    """初始化成就数据"""
    print("初始化成就数据...")
    
    achievements = [
        ("初出茅庐", "达到2级", 1, "level_up", 2, 20, 10, 1, 1),
        ("小有成就", "达到5级", 1, "level_up", 5, 50, 25, 2, 1),
        ("经验丰富", "达到10级", 1, "level_up", 10, 100, 50, 3, 1),
        ("怪物杀手", "击败100只怪物", 1, "kill_monster", 100, 200, 100, 4, 1),
        ("收藏家", "收集50个物品", 3, "collect_item", 50, 80, 40, 5, 1),
        ("技能大师", "学会10个技能", 2, "learn_skill", 10, 150, 75, 6, 1),
        ("任务专家", "完成20个任务", 2, "complete_quest", 20, 300, 150, 7, 1),
        ("传奇战士", "达到20级", 1, "level_up", 20, 500, 300, 8, 1),
        ("装备大师", "拥有10件装备", 3, "equip_item", 10, 120, 60, None, 0),
        ("末日幸存者", "在末日世界生存30天", 4, "survive_days", 30, 1000, 500, None, 0)
    ]
    
    for achievement_data in achievements:
        Achievement.create(*achievement_data)
    
    print(f"已创建 {len(achievements)} 个成就")

def main():
    """主函数"""
    print("开始初始化游戏数据...")
    
    # 首先初始化数据库表
    print("初始化数据库表...")
    init_db()
    
    # 初始化各种游戏数据
    init_monsters()
    init_enemies()
    init_items()
    init_skills()
    init_quests()
    init_achievements()
    
    print("游戏数据初始化完成！")

if __name__ == "__main__":
    main()
