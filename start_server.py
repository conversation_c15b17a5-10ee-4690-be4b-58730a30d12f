#!/usr/bin/env python3
"""
服务器启动脚本
"""

import sys
import subprocess
import os
from pathlib import Path
import time

def check_dependencies():
    """检查依赖"""
    print("检查项目依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "python-jose[cryptography]",
        "passlib[bcrypt]",
        "python-dotenv",
        "pydantic-settings"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # 简单的导入检查
            if package == "uvicorn[standard]":
                import uvicorn
            elif package == "python-multipart":
                import multipart
            elif package == "python-jose[cryptography]":
                import jose
            elif package == "passlib[bcrypt]":
                import passlib
            elif package == "python-dotenv":
                import dotenv
            elif package == "pydantic-settings":
                import pydantic_settings
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        install = input("是否自动安装缺少的依赖? (y/n): ")
        if install.lower() == 'y':
            return install_dependencies(missing_packages)
        else:
            print("请手动安装缺少的依赖包")
            return False
    
    return True

def install_dependencies(packages):
    """安装依赖包"""
    print("安装依赖包...")
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安装失败: {e}")
            return False
    
    return True

def init_database():
    """初始化数据库"""
    print("初始化数据库...")
    
    try:
        # 导入并初始化数据库
        from server.models import init_db
        init_db()
        print("✓ 数据库初始化成功")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False

def init_game_data():
    """初始化游戏数据"""
    print("初始化游戏数据...")
    
    try:
        # 运行游戏数据初始化脚本
        from server.scripts.init_game_data import main as init_data_main
        init_data_main()
        print("✓ 游戏数据初始化成功")
        return True
    except Exception as e:
        print(f"✗ 游戏数据初始化失败: {e}")
        print("可以稍后手动运行: python server/scripts/init_game_data.py")
        return False

def check_environment():
    """检查环境配置"""
    print("检查环境配置...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ .env 文件不存在")
        create_env = input("是否创建默认的 .env 文件? (y/n): ")
        if create_env.lower() == 'y':
            create_default_env()
        else:
            print("请手动创建 .env 文件")
            return False
    else:
        print("✓ .env 文件存在")
    
    return True

def create_default_env():
    """创建默认环境配置文件"""
    env_content = """# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mud_game
DB_USER=postgres
DB_PASSWORD=password

# JWT配置
SECRET_KEY=your-super-secret-key-that-is-at-least-32-characters-long-for-security
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 游戏配置
MAX_CHARACTERS_PER_USER=3
STARTING_LEVEL=1
STARTING_GOLD=100

# 调试模式
DEBUG_MODE=true
ENVIRONMENT=development
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("✓ 已创建默认 .env 文件")

def start_server():
    """启动服务器"""
    print("启动服务器...")
    
    try:
        # 导入配置
        from server.config import API_CONFIG
        
        host = API_CONFIG['api'].host
        port = API_CONFIG['api'].port
        
        print(f"服务器将在 http://{host}:{port} 启动")
        print("按 Ctrl+C 停止服务器")
        print("="*50)
        
        # 启动服务器
        import uvicorn
        from server.main import app
        
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=True,  # 开发模式下启用热重载
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器失败: {e}")

def show_api_info():
    """显示API信息"""
    print("\n" + "="*50)
    print("API 端点信息")
    print("="*50)
    
    endpoints = [
        ("POST", "/api/v1/register", "用户注册"),
        ("POST", "/api/v1/login", "用户登录"),
        ("POST", "/api/v1/characters/create", "创建角色"),
        ("GET", "/api/v1/characters/", "获取角色列表"),
        ("POST", "/api/v1/battle/start", "开始战斗"),
        ("POST", "/api/v1/battle/round", "战斗回合"),
        ("GET", "/api/v1/items/", "获取物品列表"),
        ("GET", "/api/v1/skills/", "获取技能列表"),
        ("GET", "/api/v1/quests/", "获取任务列表"),
        ("GET", "/api/v1/achievements/", "获取成就列表"),
    ]
    
    for method, endpoint, description in endpoints:
        print(f"{method:6} {endpoint:30} - {description}")
    
    print("\n访问 http://localhost:8000/docs 查看完整API文档")
    print("="*50)

def main():
    """主函数"""
    print("末日觉醒 MUD 游戏服务器启动器")
    print("="*50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，无法启动服务器")
        return
    
    # 检查环境配置
    if not check_environment():
        print("环境配置检查失败，无法启动服务器")
        return
    
    # 初始化数据库
    if not init_database():
        print("数据库初始化失败，无法启动服务器")
        return
    
    # 询问是否初始化游戏数据
    init_data = input("是否初始化游戏数据 (怪物、物品、技能等)? (y/n): ")
    if init_data.lower() == 'y':
        init_game_data()
    
    # 显示API信息
    show_api_info()
    
    # 启动服务器
    input("\n按回车键启动服务器...")
    start_server()

if __name__ == "__main__":
    main()
