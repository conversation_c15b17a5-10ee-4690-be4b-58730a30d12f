from .db import db

class Character:
    @staticmethod
    def create_table():
        """创建角色表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS character (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                level INTEGER DEFAULT 1,
                exp INTEGER DEFAULT 0,
                hp INTEGER DEFAULT 100,
                mp INTEGER DEFAULT 50,
                strength INTEGER DEFAULT 10,
                agility INTEGER DEFAULT 10,
                intelligence INTEGER DEFAULT 10,
                vitality INTEGER DEFAULT 10,
                FOREIGN KEY (user_id) REFERENCES user (id)
            )
        ''')
        conn.commit()

    @staticmethod
    def create(user_id, name):
        """创建新角色"""
        conn, cursor = db.get_db()
        cursor.execute(
            'INSERT INTO character (user_id, name) VALUES (?, ?)',
            (user_id, name)
        )
        character_id = cursor.lastrowid
        conn.commit()
        return character_id

    @staticmethod
    def get_by_user(user_id):
        """获取用户的所有角色"""
        conn, cursor = db.get_db()
        cursor.execute(
            'SELECT * FROM character WHERE user_id = ?',
            (user_id,)
        )
        return cursor.fetchall()

    @staticmethod
    def get_by_id(character_id):
        """根据ID获取角色"""
        conn, cursor = db.get_db()
        cursor.execute(
            'SELECT * FROM character WHERE id = ?',
            (character_id,)
        )
        return cursor.fetchone()

    @staticmethod
    def add_item(character_id, item_name, item_type, **kwargs):
        """给角色添加物品"""
        from .item import Item
        return Item.create(character_id, item_name, item_type, **kwargs)

    @staticmethod
    def get_items(character_id):
        """获取角色的所有物品"""
        from .item import Item
        return Item.get_by_character(character_id)

    @staticmethod
    def remove_item(character_id, item_id):
        """移除角色的物品"""
        from .item import Item
        Item.delete(item_id)

    @staticmethod
    def increase_attribute(character_id, attribute, amount=1):
        """提升角色属性"""
        valid_attributes = ['strength', 'agility', 'intelligence', 'vitality']
        if attribute not in valid_attributes:
            raise ValueError(f"无效的属性: {attribute}")
        
        conn, cursor = db.get_db()
        cursor.execute(
            f'UPDATE character SET {attribute} = {attribute} + ? WHERE id = ?',
            (amount, character_id)
        )
        conn.commit()
        return Character.get_by_id(character_id)

    @staticmethod
    def learn_skill(character_id, skill_id):
        """学习技能"""
        from .skill import Skill
        skill = Skill.get_by_id(skill_id)
        if not skill:
            raise ValueError("技能不存在")
        
        conn, cursor = db.get_db()
        # 检查是否已学习该技能
        cursor.execute(
            'SELECT * FROM character_skills WHERE character_id = ? AND skill_id = ?',
            (character_id, skill_id)
        )
        if cursor.fetchone():
            raise ValueError("已学习该技能")
        
        # 添加技能
        cursor.execute(
            'INSERT INTO character_skills (character_id, skill_id) VALUES (?, ?)',
            (character_id, skill_id)
        )
        conn.commit()
        return True

    @staticmethod
    def get_skills(character_id):
        """获取角色已学习的技能"""
        conn, cursor = db.get_db()
        cursor.execute('''
            SELECT s.* FROM skills s
            JOIN character_skills cs ON s.id = cs.skill_id
            WHERE cs.character_id = ?
        ''', (character_id,))
        return cursor.fetchall()