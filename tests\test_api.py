"""
API接口测试
"""

import pytest
from fastapi.testclient import TestClient

class TestAuthAPI:
    """认证API测试"""
    
    def test_register_success(self, client, test_user_data):
        """测试用户注册成功"""
        response = client.post("/api/v1/register", json=test_user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_register_duplicate_phone(self, client, test_user_data):
        """测试重复手机号注册"""
        # 第一次注册
        client.post("/api/v1/register", json=test_user_data)
        
        # 第二次注册相同手机号
        response = client.post("/api/v1/register", json=test_user_data)
        assert response.status_code == 400
        assert "手机号已被注册" in response.json()["detail"]
    
    def test_login_success(self, client, test_user_data):
        """测试用户登录成功"""
        # 先注册
        client.post("/api/v1/register", json=test_user_data)
        
        # 登录
        login_data = {
            "username": test_user_data["phone"],
            "password": test_user_data["password"]
        }
        response = client.post("/api/v1/login", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self, client):
        """测试无效凭据登录"""
        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
        response = client.post("/api/v1/login", data=login_data)
        assert response.status_code == 401

class TestCharacterAPI:
    """角色API测试"""
    
    def test_create_character(self, authenticated_client, test_character_data):
        """测试创建角色"""
        client, token = authenticated_client
        
        response = client.post("/api/v1/characters/create", params=test_character_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert "character_id" in data["data"]
    
    def test_create_character_invalid_name(self, authenticated_client):
        """测试创建角色时使用无效名称"""
        client, token = authenticated_client
        
        # 空名称
        response = client.post("/api/v1/characters/create", params={"name": ""})
        assert response.status_code == 400
        
        # 名称过长
        long_name = "a" * 20
        response = client.post("/api/v1/characters/create", params={"name": long_name})
        assert response.status_code == 400
    
    def test_get_characters(self, test_character):
        """测试获取角色列表"""
        character_id, client = test_character
        
        response = client.get("/api/v1/characters/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert len(data["data"]) > 0
    
    def test_get_character_detail(self, test_character):
        """测试获取角色详情"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/characters/{character_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert data["data"][0] == character_id  # ID应该匹配

class TestBattleAPI:
    """战斗API测试"""
    
    def test_start_battle(self, test_character):
        """测试开始战斗"""
        character_id, client = test_character
        
        # 假设怪物ID为1存在
        response = client.post("/api/v1/battle/start", json={"monster_id": 1})
        
        if response.status_code == 200:
            data = response.json()
            assert "battle_id" in data
            assert "character" in data
            assert "monster" in data
        else:
            # 如果没有怪物数据，应该返回404
            assert response.status_code in [400, 404]
    
    def test_battle_round(self, test_character):
        """测试战斗回合"""
        character_id, client = test_character
        
        # 先开始战斗
        start_response = client.post("/api/v1/battle/start", json={"monster_id": 1})
        
        if start_response.status_code == 200:
            battle_id = start_response.json()["battle_id"]
            
            # 执行战斗回合
            response = client.post("/api/v1/battle/round", json={"battle_id": battle_id})
            assert response.status_code == 200
            
            data = response.json()
            assert "battle_over" in data
            assert "round_result" in data

class TestItemAPI:
    """物品API测试"""
    
    def test_get_all_items(self, client):
        """测试获取所有物品"""
        response = client.get("/api/v1/items/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
    
    def test_get_character_inventory(self, test_character):
        """测试获取角色背包"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/items/character/{character_id}/inventory")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200

class TestSkillAPI:
    """技能API测试"""
    
    def test_get_all_skills(self, client):
        """测试获取所有技能"""
        response = client.get("/api/v1/skills/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
    
    def test_get_character_skills(self, test_character):
        """测试获取角色技能"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/skills/character/{character_id}/learned")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200

class TestAttributesAPI:
    """属性API测试"""
    
    def test_get_character_attributes(self, test_character):
        """测试获取角色属性"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/attributes/character/{character_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert "attributes" in data["data"]
        assert "derived_stats" in data["data"]
    
    def test_increase_attribute(self, test_character):
        """测试提升属性"""
        character_id, client = test_character
        
        attribute_data = {
            "attribute": "strength",
            "amount": 1
        }
        
        response = client.post(
            f"/api/v1/attributes/character/{character_id}/increase",
            json=attribute_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200

class TestQuestAPI:
    """任务API测试"""
    
    def test_get_all_quests(self, client):
        """测试获取所有任务"""
        response = client.get("/api/v1/quests/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
    
    def test_get_character_quests(self, test_character):
        """测试获取角色任务"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/quests/character/{character_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200

class TestAchievementAPI:
    """成就API测试"""
    
    def test_get_all_achievements(self, client):
        """测试获取所有成就"""
        response = client.get("/api/v1/achievements/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
    
    def test_get_character_achievements(self, test_character):
        """测试获取角色成就"""
        character_id, client = test_character
        
        response = client.get(f"/api/v1/achievements/character/{character_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
