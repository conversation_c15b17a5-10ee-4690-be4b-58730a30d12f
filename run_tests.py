#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def install_test_dependencies():
    """安装测试依赖"""
    print("安装测试依赖...")
    
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "httpx>=0.24.0",
        "pytest-cov>=4.0.0"
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✓ 已安装 {dep}")
        except subprocess.CalledProcessError as e:
            print(f"✗ 安装 {dep} 失败: {e}")
            return False
    
    return True

def run_tests():
    """运行测试"""
    print("\n开始运行测试...")
    
    # 确保在项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 运行测试命令
    test_commands = [
        # 运行所有测试
        [sys.executable, "-m", "pytest", "tests/", "-v"],
        
        # 运行测试并生成覆盖率报告
        [sys.executable, "-m", "pytest", "tests/", "--cov=server", "--cov-report=html", "--cov-report=term"],
    ]
    
    for i, cmd in enumerate(test_commands):
        print(f"\n{'='*50}")
        print(f"运行测试命令 {i+1}: {' '.join(cmd)}")
        print(f"{'='*50}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            print("STDOUT:")
            print(result.stdout)
            
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
            
            if result.returncode == 0:
                print(f"✓ 测试命令 {i+1} 执行成功")
            else:
                print(f"✗ 测试命令 {i+1} 执行失败 (返回码: {result.returncode})")
                
        except Exception as e:
            print(f"✗ 执行测试命令 {i+1} 时出错: {e}")

def run_specific_tests():
    """运行特定测试"""
    test_files = [
        "tests/test_models.py",
        "tests/test_api.py", 
        "tests/test_game_logic.py"
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n{'='*50}")
            print(f"运行 {test_file}")
            print(f"{'='*50}")
            
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pytest", test_file, "-v"],
                    capture_output=True,
                    text=True
                )
                
                print(result.stdout)
                if result.stderr:
                    print("错误输出:")
                    print(result.stderr)
                    
            except Exception as e:
                print(f"运行 {test_file} 时出错: {e}")
        else:
            print(f"测试文件 {test_file} 不存在")

def check_test_environment():
    """检查测试环境"""
    print("检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("警告: 建议使用Python 3.8或更高版本")
    
    # 检查必要的模块
    required_modules = [
        "fastapi",
        "uvicorn", 
        "sqlite3",
        "jwt",
        "passlib"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 已安装")
        except ImportError:
            print(f"✗ {module} 未安装")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n缺少以下模块: {', '.join(missing_modules)}")
        print("请先安装缺少的模块")
        return False
    
    return True

def main():
    """主函数"""
    print("MUD游戏测试运行器")
    print("="*50)
    
    # 检查测试环境
    if not check_test_environment():
        print("环境检查失败，退出测试")
        return
    
    # 安装测试依赖
    if not install_test_dependencies():
        print("依赖安装失败，尝试继续运行测试...")
    
    # 运行测试
    choice = input("\n选择测试模式:\n1. 运行所有测试\n2. 运行特定测试文件\n3. 退出\n请输入选择 (1-3): ")
    
    if choice == "1":
        run_tests()
    elif choice == "2":
        run_specific_tests()
    elif choice == "3":
        print("退出测试")
        return
    else:
        print("无效选择，运行所有测试")
        run_tests()
    
    print("\n测试完成!")
    print("如果生成了覆盖率报告，请查看 htmlcov/index.html")

if __name__ == "__main__":
    main()
