from map_system import MapSystem
from character_system import Character, CharacterClass
from battle_system import BattleSystem
import random

class GameSystem:
    """游戏主系统，管理各子系统交互"""
    
    def __init__(self):
        self.map_system = MapSystem()
        self.player = None
        self.game_active = False
        self.battle_system = None
        self.in_battle = False
        
    def initialize_game(self):
        """初始化游戏"""
        if not self.map_system.load_from_json("map_data.json"):
            print("地图加载失败，无法启动游戏")
            return False
            
        print("欢迎来到《末日觉醒》MUD游戏!")
        print("请创建你的角色:")
        
        # 显示职业选择
        for i, char_class in enumerate(CharacterClass):
            print(f"{i+1}. {char_class.value}")
            
        # 获取玩家选择
        while True:
            try:
                choice = int(input("请选择职业(1-4): "))
                if 1 <= choice <= 4:
                    selected_class = list(CharacterClass)[choice-1]
                    break
            except ValueError:
                print("请输入有效数字")
                
        name = input("请输入角色名称: ")
        self.player = Character(name, selected_class)
        self.game_active = True
        print(f"\n角色 {name} ({selected_class.value}) 创建成功!")
        print("游戏开始!")
        return True
        
    def show_main_menu(self):
        """显示主菜单"""
        print("\n请选择操作:")
        print("1. 移动")
        print("2. 查看状态")
        print("3. 查看技能")
        print("4. 退出游戏")
        
    def show_battle_menu(self):
        """显示战斗菜单"""
        print("\n战斗选项:")
        print("1. 攻击")
        print("2. 使用技能")
        print("3. 逃跑")
        print("4. 查看状态")
        
    def show_move_menu(self):
        """显示移动菜单"""
        print("\n可移动地点:")
        if not self.map_system.current_node or not self.map_system.current_node.children:
            print("当前没有可移动的地点")
            return
            
        for i, child in enumerate(self.map_system.current_node.children):
            print(f"{i+1}. {child.name}")
        print(f"{len(self.map_system.current_node.children)+1}. 取消")
        
    def show_skills_menu(self):
        """显示技能菜单"""
        print("\n可用技能:")
        for i, skill in enumerate(self.player.skills):
            cooldown = f"(冷却中，剩余{skill.cooldown}回合)" if skill.cooldown > 0 else ""
            print(f"{i+1}. {skill.name}: {skill.description} {cooldown}")
        print(f"{len(self.player.skills)+1}. 返回")
        
    def process_menu_choice(self):
        """处理菜单选择"""
        if not self.game_active:
            return False
            
        # 战斗状态下的菜单处理
        if self.in_battle:
            self.show_battle_menu()
            try:
                choice = int(input("请选择操作(1-4): "))
                if choice == 1:  # 普通攻击
                    result = self.battle_system.player_turn(1)
                    if result in ["victory", "defeat"]:
                        self.battle_system.show_battle_log()
                        self.in_battle = False
                elif choice == 2:  # 使用技能
                    self.show_skills_menu()
                    skill_choice = int(input("请选择技能: "))
                    if 1 <= skill_choice <= len(self.player.skills):
                        result = self.battle_system.player_turn(2, skill_choice-1)
                        if result in ["victory", "defeat"]:
                            self.battle_system.show_battle_log()
                            self.in_battle = False
                elif choice == 3:
                    if random.random() < 0.5:
                        print("成功逃脱!")
                        self.in_battle = False
                    else:
                        print("逃跑失败!")
                        self.battle_system.enemy_turn()
                elif choice == 4:
                    self.show_status()
                else:
                    print("无效选择")
            except ValueError:
                print("请输入有效数字")
            return self.game_active
            
        # 非战斗状态下的菜单处理
        self.show_main_menu()
        try:
            choice = int(input("请选择操作(1-4): "))
            if choice == 1:
                self.show_move_menu()
                move_choice = int(input("请选择移动地点: "))
                if not self.map_system.current_node or not self.map_system.current_node.children:
                    return
                    
                if 1 <= move_choice <= len(self.map_system.current_node.children):
                    location = self.map_system.current_node.children[move_choice-1].name
                    self.handle_move(location)
                elif move_choice == len(self.map_system.current_node.children)+1:
                    pass  # 取消
                else:
                    print("无效选择")
            elif choice == 2:
                self.show_status()
            elif choice == 3:
                self.show_skills()
            elif choice == 4:
                self.game_active = False
                print("游戏结束，感谢游玩!")
            else:
                print("无效选择")
        except ValueError:
            print("请输入有效数字")
            
        return self.game_active
        
    def handle_move(self, location: str):
        """处理移动命令"""
        if self.map_system.move_to(location):
            print(f"\n你已到达 {location}")
            self.map_system.print_current_location()
            
            # 随机事件触发
            self.trigger_random_event()
        else:
            print(f"无法移动到 {location}，请输入有效地点")
            
    def trigger_random_event(self):
        """触发随机事件"""
        event_chance = random.random()
        current_node = self.map_system.current_node
        
        if event_chance < 0.3:
            # 30%几率触发事件
            if "monster_rate" in current_node.game_attributes:
                monster_rate = current_node.game_attributes["monster_rate"]
                if random.random() < monster_rate:
                    print("\n遭遇了怪物!")
                    enemy_level = max(1, self.player.attributes.level - 1 + random.randint(0, 2))
                    enemy_type = random.choice(["变异鼠", "辐射蟑螂", "丧尸犬",'变异狼','编译狗','变异熊','变异蛇','变异猫','变异猪','变异牛','变异羊','变异马','变异兔','变异鹰','变异鸟','变异蝙蝠','变异蜘蛛','变异蛇','变异蜥蜴','变异龟','变异鱼','变异鳄鱼'])
                    self.battle_system = BattleSystem(self.player)
                    self.battle_system.start_battle(enemy_type, enemy_level)
                    self.in_battle = True
                else:
                    print("\n发现了一些资源!")
                    # 这里将实现资源收集逻辑
                    
    def show_status(self):
        """显示角色状态"""
        if self.player:
            print(f"\n角色: {self.player.name} ({self.player.char_class.value})")
            print(f"等级: {self.player.attributes.level}")
            print(f"HP: {self.player.attributes.hp}/{self.player.attributes.max_hp}")
            print(f"攻击: {self.player.attributes.attack}")
            print(f"防御: {self.player.attributes.defense}")
            
    def show_skills(self):
        """显示角色技能"""
        if self.player and self.player.skills:
            print("\n可用技能:")
            for skill in self.player.skills:
                cooldown = f"(冷却中，剩余{skill.cooldown}回合)" if skill.cooldown > 0 else ""
                print(f"- {skill.name}: {skill.description} {cooldown}")

if __name__ == "__main__":
    game = GameSystem()
    if game.initialize_game():
        while game.game_active:
            game.process_menu_choice()