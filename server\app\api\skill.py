from fastapi import APIRouter, Depends, HTTPException
from server.models.character import Character
from server.models.skill import Skill
from server.core.auth import get_current_user
from typing import Dict, Any

router = APIRouter()

@router.get("/")
async def get_all_skills():
    """获取所有可学技能"""
    return Skill.get_all()

@router.post("/{character_id}/learn")
async def learn_skill(
    character_id: int,
    skill_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """学习技能"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    try:
        skill_id = skill_data.get("skill_id")
        if not skill_id:
            raise ValueError("需要提供技能ID")
        
        success = Character.learn_skill(character_id, skill_id)
        return {
            "success": success,
            "message": "技能学习成功"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{character_id}/learned")
async def get_learned_skills(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色已学技能"""
    character = Character.get_by_id(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    if character["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="无权操作该角色")
    
    return Character.get_skills(character_id)