from server.core.db import db

class Enemy:
    """敌人模型"""
    def __init__(self):
        self.table_name = "enemies"
        self.id = None
        self.name = ""
        self.hp = 0
        self.attack = 0
        self.defense = 0
        self.agility = 0
        self.exp_reward = 0
        self.description = ""

    @staticmethod
    def create_table():
        """创建敌人表"""
        conn, cursor = db.get_db()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enemies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                hp INTEGER NOT NULL,
                attack INTEGER NOT NULL,
                defense INTEGER NOT NULL,
                agility INTEGER NOT NULL,
                exp_reward INTEGER NOT NULL,
                description TEXT
            )
        ''')
        conn.commit()

    @staticmethod
    def create(name, hp, attack, defense, agility, exp_reward, description=""):
        """创建新敌人"""
        conn, cursor = db.get_db()
        cursor.execute('''
            INSERT INTO enemies 
            (name, hp, attack, defense, agility, exp_reward, description)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (name, hp, attack, defense, agility, exp_reward, description))
        conn.commit()
        return cursor.lastrowid

    @staticmethod
    def get_by_id(enemy_id):
        """根据ID获取敌人"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM enemies WHERE id = ?', (enemy_id,))
        return cursor.fetchone()

    @staticmethod
    def get_all():
        """获取所有敌人"""
        conn, cursor = db.get_db()
        cursor.execute('SELECT * FROM enemies')
        return cursor.fetchall()