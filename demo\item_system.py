from enum import Enum
from typing import Optional

class ItemType(Enum):
    """道具类型枚举"""
    CONSUMABLE = 1  # 消耗品
    EQUIPMENT = 2   # 装备
    MATERIAL = 3     # 材料

class Item:
    """基础道具类"""
    def __init__(self, item_id: int, name: str, description: str, item_type: ItemType, max_stack: int = 1):
        self.item_id = item_id
        self.name = name
        self.description = description
        self.item_type = item_type
        self.max_stack = max(max_stack, 1)  # 确保至少为1
        self.quantity = 1  # 当前堆叠数量
    
    def use(self, target) -> bool:
        """使用道具的抽象方法"""
        raise NotImplementedError("子类必须实现use方法")

class HealthPotion(Item):
    """生命恢复药剂"""
    def __init__(self, item_id: int, restore_amount: int, max_stack: int = 10):
        super().__init__(
            item_id=item_id,
            name="生命药剂",
            description=f"恢复{restore_amount}点生命值",
            item_type=ItemType.CONSUMABLE,
            max_stack=max_stack
        )
        self.restore_amount = restore_amount
    
    def use(self, target) -> bool:
        """使用生命药剂"""
        if target.hp >= target.max_hp:
            return False  # 生命值已满，使用失败
        
        target.hp = min(target.hp + self.restore_amount, target.max_hp)
        return True

class ManaPotion(Item):
    """魔法恢复药剂"""
    def __init__(self, item_id: int, restore_amount: int, max_stack: int = 10):
        super().__init__(
            item_id=item_id,
            name="魔法药剂",
            description=f"恢复{restore_amount}点魔法值",
            item_type=ItemType.CONSUMABLE,
            max_stack=max_stack
        )
        self.restore_amount = restore_amount
    
    def use(self, target) -> bool:
        """使用魔法药剂"""
        if target.mp >= target.max_mp:
            return False  # 魔法值已满，使用失败
        
        target.mp = min(target.mp + self.restore_amount, target.max_mp)
        return True